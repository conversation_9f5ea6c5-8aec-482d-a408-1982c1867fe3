// Real integration test for the browsing automation fixes
// This actually calls the RouKey API to test browsing functionality

const fetch = require('node-fetch');
require('dotenv').config({ path: '.env.local' });

const ROKEY_API_URL = 'http://localhost:3000/api/v1/chat/completions';
const API_KEY = process.env.ROKEY_API_ACCESS_TOKEN;

async function testRealBrowsing() {
  console.log('🧪 Testing REAL browsing automation with RouKey API...');
  console.log('🔑 Using API key:', API_KEY ? 'Found' : 'Missing');

  if (!API_KEY) {
    console.error('❌ ROKEY_API_ACCESS_TOKEN not found in .env.local');
    return;
  }

  const testCases = [
    {
      name: 'Restaurant Reservation Query',
      query: 'find restaurants open for dinner tonight in New York',
      description: 'Should use restaurant sites like OpenTable, not flight sites'
    },
    {
      name: 'Job Search Query', 
      query: 'find software engineer jobs in San Francisco',
      description: 'Should use job sites like LinkedIn, Indeed, not flight sites'
    },
    {
      name: 'Shopping Query',
      query: 'find wireless headphones under $100',
      description: 'Should use shopping sites like Amazon, not flight sites'
    },
    {
      name: 'Flight Query (should still work)',
      query: 'find flights from New York to Los Angeles tomorrow',
      description: 'Should use flight sites like Kayak, Expedia'
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n🔍 Testing: ${testCase.name}`);
    console.log(`📝 Query: "${testCase.query}"`);
    console.log(`🎯 Expected: ${testCase.description}`);

    try {
      const response = await fetch(ROKEY_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${API_KEY}`
        },
        body: JSON.stringify({
          model: 'auto',
          messages: [
            {
              role: 'user',
              content: testCase.query
            }
          ],
          stream: false,
          max_tokens: 1000
        })
      });

      if (!response.ok) {
        console.error(`❌ API Error: ${response.status} ${response.statusText}`);
        const errorText = await response.text();
        console.error('Error details:', errorText);
        continue;
      }

      const result = await response.json();
      
      if (result.choices && result.choices[0]) {
        const content = result.choices[0].message.content;
        console.log('✅ Response received');
        
        // Check if browsing was triggered
        if (content.includes('browsing') || content.includes('searching') || content.includes('found')) {
          console.log('🌐 Browsing appears to have been triggered');
          
          // Look for signs that the right sites were used
          const lowerContent = content.toLowerCase();
          
          if (testCase.name.includes('Restaurant')) {
            if (lowerContent.includes('opentable') || lowerContent.includes('yelp') || lowerContent.includes('restaurant')) {
              console.log('✅ Correctly used restaurant-related sites/data');
            } else if (lowerContent.includes('flight') || lowerContent.includes('airline')) {
              console.log('❌ Incorrectly used flight sites for restaurant query');
            }
          } else if (testCase.name.includes('Job')) {
            if (lowerContent.includes('linkedin') || lowerContent.includes('indeed') || lowerContent.includes('job')) {
              console.log('✅ Correctly used job-related sites/data');
            } else if (lowerContent.includes('flight') || lowerContent.includes('airline')) {
              console.log('❌ Incorrectly used flight sites for job query');
            }
          } else if (testCase.name.includes('Shopping')) {
            if (lowerContent.includes('amazon') || lowerContent.includes('price') || lowerContent.includes('product')) {
              console.log('✅ Correctly used shopping-related sites/data');
            } else if (lowerContent.includes('flight') || lowerContent.includes('airline')) {
              console.log('❌ Incorrectly used flight sites for shopping query');
            }
          } else if (testCase.name.includes('Flight')) {
            if (lowerContent.includes('flight') || lowerContent.includes('airline') || lowerContent.includes('kayak')) {
              console.log('✅ Correctly used flight-related sites/data');
            }
          }
          
        } else {
          console.log('⚠️ Browsing may not have been triggered (or completed without browsing)');
        }
        
        // Show first 200 characters of response
        console.log('📄 Response preview:', content.substring(0, 200) + '...');
        
      } else {
        console.error('❌ Unexpected response format:', result);
      }

    } catch (error) {
      console.error(`❌ Test failed for ${testCase.name}:`, error.message);
    }

    // Wait between tests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  console.log('\n📊 Test Summary:');
  console.log('- If browsing was triggered and used appropriate sites for each query type, the fixes are working');
  console.log('- If all queries went to flight sites, the old hardcoded behavior is still active');
  console.log('- If browsing failed completely, there may be BrowserQL syntax issues');
}

// Test BrowserQL syntax generation (without actually calling Browserless)
function testBrowserQLGeneration() {
  console.log('\n🔧 Testing BrowserQL syntax generation...');
  
  // Simulate the workflow generation
  const sampleWorkflow = [
    {
      name: 'navigate',
      type: 'navigate', 
      params: { url: 'https://example.com', waitUntil: 'networkIdle' }
    },
    {
      name: 'optional_click',
      type: 'click',
      params: { selector: '.cookie-accept', optional: true, timeout: 5000 }
    },
    {
      name: 'search_input',
      type: 'type',
      params: { selector: 'input[type="search"]', text: 'test query' }
    },
    {
      name: 'extract_results',
      type: 'extract',
      params: { selector: '.results', type: 'html' }
    }
  ];

  console.log('Generated workflow steps:');
  sampleWorkflow.forEach((step, index) => {
    console.log(`${index + 1}. ${step.name} (${step.type}):`, JSON.stringify(step.params, null, 2));
  });

  console.log('\n✅ Workflow generation test completed');
}

async function runAllTests() {
  console.log('🚀 Starting comprehensive browsing automation tests...\n');
  
  // Test 1: BrowserQL syntax generation
  testBrowserQLGeneration();
  
  // Test 2: Real API calls
  await testRealBrowsing();
  
  console.log('\n🏁 All tests completed!');
}

// Run the tests
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { testRealBrowsing, testBrowserQLGeneration };
