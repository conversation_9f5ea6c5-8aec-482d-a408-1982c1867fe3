"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_browsing_BrowsingExecutionService_ts";
exports.ids = ["_rsc_src_lib_browsing_BrowsingExecutionService_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/browserless.ts":
/*!********************************!*\
  !*** ./src/lib/browserless.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Browserless.io API service with key rotation\n// Similar to Jina API key rotation system\nclass BrowserlessService {\n    constructor(){\n        this.apiKeys = [];\n        this.currentKeyIndex = 0;\n        this.keyUsageCount = new Map();\n        this.keyErrors = new Map();\n        this.MAX_RETRIES = 3;\n        this.ERROR_THRESHOLD = 5;\n        this.ENDPOINT = 'https://production-sfo.browserless.io';\n        this.initializeKeys();\n        this.testConnectivity();\n    }\n    static getInstance() {\n        if (!BrowserlessService.instance) {\n            BrowserlessService.instance = new BrowserlessService();\n        }\n        return BrowserlessService.instance;\n    }\n    initializeKeys() {\n        // Load all Browserless API keys from environment (similar to Jina pattern)\n        this.apiKeys = [\n            process.env.BROWSERLESS_API_KEY,\n            process.env.BROWSERLESS_API_KEY_2,\n            process.env.BROWSERLESS_API_KEY_3,\n            process.env.BROWSERLESS_API_KEY_4,\n            process.env.BROWSERLESS_API_KEY_5,\n            process.env.BROWSERLESS_API_KEY_6,\n            process.env.BROWSERLESS_API_KEY_7,\n            process.env.BROWSERLESS_API_KEY_8,\n            process.env.BROWSERLESS_API_KEY_9,\n            process.env.BROWSERLESS_API_KEY_10\n        ].filter(Boolean);\n        if (this.apiKeys.length === 0) {\n            console.error('No Browserless API keys found in environment variables');\n            return;\n        }\n        console.log(`[Browserless] Initialized with ${this.apiKeys.length} API keys`);\n        // Initialize usage tracking\n        this.apiKeys.forEach((key)=>{\n            this.keyUsageCount.set(key, 0);\n            this.keyErrors.set(key, 0);\n        });\n    }\n    /**\n   * Test connectivity to Browserless service\n   */ async testConnectivity() {\n        if (this.apiKeys.length === 0) {\n            console.warn('[Browserless] No API keys available for connectivity test');\n            return;\n        }\n        try {\n            const testKey = this.apiKeys[0];\n            const testUrl = `${this.ENDPOINT}/function?token=${testKey}`;\n            // Simple connectivity test with proper function format\n            const testCode = `\n        export default async function ({ page }) {\n          return {\n            status: \"connectivity-test-success\",\n            timestamp: new Date().toISOString()\n          };\n        }\n      `;\n            const response = await fetch(testUrl, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/javascript'\n                },\n                body: testCode,\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            if (response.ok) {\n                const result = await response.text();\n                console.log('[Browserless] ✅ Connectivity test successful:', result);\n            } else {\n                const errorText = await response.text();\n                console.warn(`[Browserless] ⚠️ Connectivity test failed with status: ${response.status}`);\n                console.warn(`[Browserless] Error details: ${errorText}`);\n                console.warn(`[Browserless] Using key: ${testKey.substring(0, 8)}...`);\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.warn(`[Browserless] ⚠️ Connectivity test failed: ${errorMessage}`);\n            // Check for specific network errors\n            if (errorMessage.includes('ENOTFOUND')) {\n                console.error('[Browserless] 🌐 DNS resolution failed - check if chrome.browserless.io is accessible');\n            } else if (errorMessage.includes('ECONNRESET')) {\n                console.error('[Browserless] 🔌 Connection reset - possible network or service issue');\n            }\n        }\n    }\n    getNextApiKey() {\n        if (this.apiKeys.length === 0) {\n            throw new Error('No Browserless API keys available');\n        }\n        // Find the key with lowest usage and errors\n        let bestKey = this.apiKeys[0];\n        let bestScore = this.calculateKeyScore(bestKey);\n        for (const key of this.apiKeys){\n            const score = this.calculateKeyScore(key);\n            if (score < bestScore) {\n                bestKey = key;\n                bestScore = score;\n            }\n        }\n        return bestKey;\n    }\n    calculateKeyScore(key) {\n        const usage = this.keyUsageCount.get(key) || 0;\n        const errors = this.keyErrors.get(key) || 0;\n        // Higher score = worse key (more usage + more errors)\n        return usage + errors * 10;\n    }\n    incrementKeyUsage(key) {\n        const currentUsage = this.keyUsageCount.get(key) || 0;\n        this.keyUsageCount.set(key, currentUsage + 1);\n    }\n    incrementKeyError(key) {\n        const currentErrors = this.keyErrors.get(key) || 0;\n        this.keyErrors.set(key, currentErrors + 1);\n    }\n    isKeyHealthy(key) {\n        const errors = this.keyErrors.get(key) || 0;\n        return errors < this.ERROR_THRESHOLD;\n    }\n    getHealthyKeys() {\n        return this.apiKeys.filter((key)=>this.isKeyHealthy(key));\n    }\n    async executeFunction(code, context, config) {\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            // Reset error counts if all keys are unhealthy\n            this.keyErrors.clear();\n            this.apiKeys.forEach((key)=>this.keyErrors.set(key, 0));\n            console.log('All Browserless keys were unhealthy, resetting error counts');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.MAX_RETRIES; attempt++){\n            try {\n                const apiKey = this.getNextApiKey();\n                this.incrementKeyUsage(apiKey);\n                const response = await this.makeRequest(apiKey, code, context, config);\n                // Success - return the response\n                return response;\n            } catch (error) {\n                lastError = error;\n                console.error(`Browserless attempt ${attempt + 1} failed:`, error);\n                // If it's a rate limit or quota error, mark the key as having an error\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All Browserless API attempts failed');\n    }\n    async makeRequest(apiKey, code, context, config) {\n        const url = `${this.ENDPOINT}/function?token=${apiKey}`;\n        const requestBody = context ? {\n            code,\n            context\n        } : code;\n        const headers = {\n            'Content-Type': context ? 'application/json' : 'application/javascript',\n            'User-Agent': config?.userAgent || 'RouKey-Browser-Agent/1.0'\n        };\n        const response = await fetch(url, {\n            method: 'POST',\n            headers,\n            body: context ? JSON.stringify(requestBody) : code,\n            signal: AbortSignal.timeout(config?.timeout || 30000)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(`Browserless API error: ${response.status} - ${errorText}`);\n        }\n        const result = await response.json();\n        return result;\n    }\n    isRateLimitError(error) {\n        const message = error.message.toLowerCase();\n        return message.includes('rate limit') || message.includes('quota') || message.includes('429') || message.includes('too many requests');\n    }\n    // Convenience methods for common browser tasks\n    async navigateAndExtract(url, selector) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n        \n        const title = await page.title();\n        const content = ${selector ? `await page.$eval(\"${selector}\", el => el.textContent || el.innerText)` : 'await page.evaluate(() => document.body.innerText)'};\n        \n        return {\n          data: {\n            url: \"${url}\",\n            title,\n            content: content?.trim() || \"\"\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Advanced BrowserQL automation for complex browsing tasks\n   * Supports form filling, CAPTCHA solving, multi-step workflows, and state-of-the-art browsing\n   */ async executeBrowserQLAutomation(automationScript, options = {}) {\n        const { timeout = 60000, humanLike = true, solveCaptcha = true, sessionId, screenshots = false } = options;\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            throw new Error('No healthy Browserless API keys available');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.maxRetries; attempt++){\n            try {\n                const currentKey = this.getNextApiKey();\n                console.log(`[Browserless] BrowserQL automation attempt ${attempt + 1} with key: ${currentKey.name}`);\n                // Build the endpoint URL\n                let endpoint = `https://production-sfo.browserless.io/chromium/bql?token=${currentKey.key}`;\n                if (sessionId) {\n                    endpoint += `&sessionId=${sessionId}`;\n                }\n                const response = await fetch(endpoint, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'User-Agent': 'RouKey-Browser-Automation/1.0'\n                    },\n                    body: JSON.stringify({\n                        query: automationScript,\n                        variables: {\n                            timeout,\n                            humanLike,\n                            solveCaptcha,\n                            screenshots\n                        }\n                    }),\n                    signal: AbortSignal.timeout(timeout + 10000) // Add buffer to timeout\n                });\n                if (!response.ok) {\n                    throw new Error(`BrowserQL automation failed: ${response.status} ${response.statusText}`);\n                }\n                const result = await response.json();\n                if (result.errors && result.errors.length > 0) {\n                    throw new Error(`BrowserQL errors: ${result.errors.map((e)=>e.message).join(', ')}`);\n                }\n                console.log(`[Browserless] ✅ BrowserQL automation successful`);\n                return {\n                    data: result.data,\n                    type: \"application/json\",\n                    sessionId: result.sessionId,\n                    screenshots: result.screenshots\n                };\n            } catch (error) {\n                lastError = error;\n                console.error(`[Browserless] BrowserQL automation attempt ${attempt + 1} failed:`, error);\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All BrowserQL automation attempts failed');\n    }\n    /**\n   * Create a persistent browsing session for complex multi-step workflows\n   */ async createBrowsingSession(initialUrl, options = {}) {\n        const { timeout = 300000, humanLike = true, blockResources = [\n            '*.png',\n            '*.jpg',\n            '*.gif',\n            '*.mp4',\n            '*.css'\n        ] } = options;\n        const sessionScript = `\n      mutation CreateBrowsingSession {\n        ${blockResources.length > 0 ? `\n        setRequestInterception(enabled: true)\n        reject(patterns: ${JSON.stringify(blockResources)})\n        ` : ''}\n\n        goto(url: \"${initialUrl}\", waitUntil: networkIdle) {\n          status\n          time\n        }\n\n        ${humanLike ? `\n        # Add human-like behavior\n        waitForTimeout(time: ${Math.floor(Math.random() * 2000) + 1000}) {\n          time\n        }\n        ` : ''}\n\n        reconnect(timeout: ${timeout}) {\n          BrowserQLEndpoint\n        }\n      }\n    `;\n        const result = await this.executeBrowserQLAutomation(sessionScript, {\n            timeout\n        });\n        return {\n            sessionId: result.sessionId || 'default',\n            reconnectUrl: result.data.reconnect.BrowserQLEndpoint\n        };\n    }\n    /**\n   * Advanced form filling with human-like behavior\n   */ async fillFormAdvanced(sessionUrl, formData, submitSelector) {\n        const formScript = `\n      mutation FillFormAdvanced {\n        ${formData.map((field, index)=>{\n            const delay = field.delay || [\n                50,\n                150\n            ];\n            return `\n            field${index}: ${field.type === 'select' ? 'select' : field.type === 'checkbox' || field.type === 'radio' ? 'click' : 'type'}(\n              selector: \"${field.selector}\"\n              ${field.type !== 'checkbox' && field.type !== 'radio' ? `text: \"${field.value}\"` : ''}\n              ${field.type === 'text' || field.type === 'email' || field.type === 'password' ? `delay: [${delay[0]}, ${delay[1]}]` : ''}\n              visible: true\n            ) {\n              time\n              ${field.type === 'text' || field.type === 'email' || field.type === 'password' ? 'text' : 'x'}\n            }\n          `;\n        }).join('\\n')}\n\n        ${submitSelector ? `\n        submitForm: click(\n          selector: \"${submitSelector}\"\n          visible: true\n        ) {\n          time\n          x\n          y\n        }\n        ` : ''}\n\n        # Wait for any page changes after form submission\n        waitForTimeout(time: 2000) {\n          time\n        }\n      }\n    `;\n        return await this.executeBrowserQLWithSession(sessionUrl, formScript);\n    }\n    /**\n   * Execute BrowserQL script with existing session\n   */ async executeBrowserQLWithSession(sessionUrl, script) {\n        try {\n            const response = await fetch(sessionUrl, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: script\n                })\n            });\n            if (!response.ok) {\n                throw new Error(`Session execution failed: ${response.status} ${response.statusText}`);\n            }\n            const result = await response.json();\n            if (result.errors && result.errors.length > 0) {\n                throw new Error(`Session errors: ${result.errors.map((e)=>e.message).join(', ')}`);\n            }\n            return result;\n        } catch (error) {\n            console.error('[Browserless] Session execution failed:', error);\n            throw error;\n        }\n    }\n    async searchAndExtractUnblocked(query, searchEngine = 'google') {\n        const searchUrl = searchEngine === 'google' ? `https://www.google.com/search?q=${encodeURIComponent(query)}` : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            throw new Error('No healthy Browserless API keys available');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.MAX_RETRIES; attempt++){\n            try {\n                const apiKey = this.getNextApiKey();\n                this.incrementKeyUsage(apiKey);\n                const url = `${this.ENDPOINT}/unblock?token=${apiKey}`;\n                const requestBody = {\n                    url: searchUrl,\n                    content: true,\n                    browserWSEndpoint: false,\n                    cookies: false,\n                    screenshot: false,\n                    waitForSelector: {\n                        selector: 'h3, .g h3, .LC20lb, .b_algo h2',\n                        timeout: 10000\n                    }\n                };\n                const response = await fetch(url, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'User-Agent': 'RouKey-Browser-Agent/1.0'\n                    },\n                    body: JSON.stringify(requestBody),\n                    signal: AbortSignal.timeout(60000)\n                });\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    throw new Error(`Browserless unblock API error: ${response.status} - ${errorText}`);\n                }\n                const result = await response.json();\n                if (result.content) {\n                    // Parse the HTML content to extract search results\n                    const searchResults = this.parseSearchResults(result.content, searchEngine, query);\n                    return {\n                        data: {\n                            query,\n                            searchEngine,\n                            results: searchResults,\n                            timestamp: new Date().toISOString(),\n                            debug: {\n                                pageTitle: 'Unblocked search',\n                                pageUrl: searchUrl,\n                                totalElements: searchResults.length,\n                                usedSelector: 'unblock-api',\n                                extractedCount: searchResults.length\n                            }\n                        },\n                        type: \"application/json\"\n                    };\n                } else {\n                    throw new Error('No content returned from unblock API');\n                }\n            } catch (error) {\n                lastError = error;\n                console.error(`Browserless unblock attempt ${attempt + 1} failed:`, error);\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All Browserless unblock API attempts failed');\n    }\n    /**\n   * Handle infinite scroll and pagination automatically\n   */ async handleInfiniteScroll(sessionUrl, options = {}) {\n        const { maxScrolls = 10, scrollDelay = 2000, contentSelector = 'body', stopCondition = 'no-more-content' } = options;\n        const scrollScript = `\n      mutation HandleInfiniteScroll {\n        # Get initial content count\n        initialContent: text(selector: \"${contentSelector}\") {\n          text\n        }\n\n        # Perform scrolling with content monitoring\n        ${Array.from({\n            length: maxScrolls\n        }, (_, i)=>`\n          scroll${i}: scroll(\n            direction: down\n            distance: 1000\n          ) {\n            time\n          }\n\n          waitAfterScroll${i}: waitForTimeout(time: ${scrollDelay}) {\n            time\n          }\n\n          contentCheck${i}: text(selector: \"${contentSelector}\") {\n            text\n          }\n        `).join('\\n')}\n\n        # Get final content\n        finalContent: text(selector: \"${contentSelector}\") {\n          text\n        }\n      }\n    `;\n        return await this.executeBrowserQLWithSession(sessionUrl, scrollScript);\n    }\n    /**\n   * Advanced content extraction with multiple strategies\n   */ async extractContentAdvanced(sessionUrl, extractionRules) {\n        const extractionScript = `\n      mutation ExtractContentAdvanced {\n        ${extractionRules.map((rule, index)=>{\n            if (rule.type === 'screenshot') {\n                return `\n              ${rule.name}: screenshot(\n                selector: \"${rule.selector}\"\n                fullPage: false\n              ) {\n                data\n              }\n            `;\n            } else if (rule.type === 'attribute') {\n                return `\n              ${rule.name}: html(\n                selector: \"${rule.selector}\"\n                visible: true\n              ) {\n                html\n              }\n            `;\n            } else {\n                return `\n              ${rule.name}: ${rule.type}(\n                selector: \"${rule.selector}\"\n                visible: true\n              ) {\n                ${rule.type}\n              }\n            `;\n            }\n        }).join('\\n')}\n      }\n    `;\n        return await this.executeBrowserQLWithSession(sessionUrl, extractionScript);\n    }\n    /**\n   * Complex multi-step automation workflow\n   */ async executeComplexWorkflow(initialUrl, workflow) {\n        // Create session first\n        const session = await this.createBrowsingSession(initialUrl, {\n            timeout: 600000,\n            humanLike: true\n        });\n        const workflowScript = `\n      mutation ComplexWorkflow {\n        ${workflow.map((step, index)=>{\n            switch(step.type){\n                case 'navigate':\n                    return `\n                step${index}_navigate: goto(\n                  url: \"${step.params.url}\"\n                  waitUntil: ${step.params.waitUntil || 'networkIdle'}\n                ) {\n                  status\n                  time\n                }\n              `;\n                case 'click':\n                    return `\n                step${index}_click: click(\n                  selector: \"${step.params.selector}\"\n                  visible: true\n                  ${step.params.timeout ? `timeout: ${step.params.timeout}` : ''}\n                ) {\n                  time\n                  x\n                  y\n                }\n              `;\n                case 'type':\n                    return `\n                step${index}_type: type(\n                  selector: \"${step.params.selector}\"\n                  text: \"${step.params.text}\"\n                  ${step.params.delay ? `delay: [${step.params.delay[0]}, ${step.params.delay[1]}]` : ''}\n                  visible: true\n                ) {\n                  time\n                  text\n                }\n              `;\n                case 'wait':\n                    return `\n                step${index}_wait: waitForTimeout(time: ${step.params.time || 1000}) {\n                  time\n                }\n              `;\n                case 'extract':\n                    return `\n                step${index}_extract: ${step.params.type || 'text'}(\n                  selector: \"${step.params.selector}\"\n                  visible: true\n                ) {\n                  ${step.params.type || 'text'}\n                }\n              `;\n                case 'scroll':\n                    return `\n                step${index}_scroll: scroll(\n                  direction: ${step.params.direction || 'down'}\n                  distance: ${step.params.distance || 500}\n                ) {\n                  time\n                }\n              `;\n                case 'captcha':\n                    return `\n                step${index}_captcha: verify(\n                  type: ${step.params.type || 'hcaptcha'}\n                ) {\n                  solved\n                  time\n                }\n              `;\n                case 'screenshot':\n                    return `\n                step${index}_screenshot: screenshot(\n                  ${step.params.selector ? `selector: \"${step.params.selector}\"` : ''}\n                  fullPage: ${step.params.fullPage || false}\n                ) {\n                  data\n                }\n              `;\n                default:\n                    return `# Unknown step type: ${step.type}`;\n            }\n        }).join('\\n')}\n\n        # Final status check\n        finalStatus: html(visible: false) {\n          html\n        }\n      }\n    `;\n        try {\n            const result = await this.executeBrowserQLWithSession(session.reconnectUrl, workflowScript);\n            return {\n                ...result,\n                sessionId: session.sessionId,\n                reconnectUrl: session.reconnectUrl\n            };\n        } catch (error) {\n            console.error('[Browserless] Complex workflow failed:', error);\n            throw error;\n        }\n    }\n    parseSearchResults(htmlContent, searchEngine, query) {\n        // Modern search result parsing with comprehensive snippet extraction\n        // Updated for 2025 Google/Bing HTML structures to provide accurate, current information\n        const results = [];\n        if (searchEngine === 'google') {\n            // Updated Google search result patterns for 2025\n            // Google now uses more dynamic class names and data attributes\n            const modernResultPatterns = [\n                // Main search result containers (2025 patterns)\n                /<div[^>]*data-ved=\"[^\"]*\"[^>]*class=\"[^\"]*g[^\"]*\"[^>]*>(.*?)<\\/div>/gis,\n                /<div[^>]*class=\"[^\"]*MjjYud[^\"]*\"[^>]*>(.*?)<\\/div>/gis,\n                /<div[^>]*class=\"[^\"]*kvH3mc[^\"]*\"[^>]*>(.*?)<\\/div>/gis,\n                /<div[^>]*class=\"[^\"]*N54PNb[^\"]*\"[^>]*>(.*?)<\\/div>/gis\n            ];\n            // Modern title patterns\n            const titlePatterns = [\n                /<h3[^>]*class=\"[^\"]*LC20lb[^\"]*\"[^>]*>([^<]+)<\\/h3>/i,\n                /<h3[^>]*class=\"[^\"]*DKV0Md[^\"]*\"[^>]*>([^<]+)<\\/h3>/i,\n                /<h3[^>]*>([^<]+)<\\/h3>/i\n            ];\n            // Modern link patterns\n            const linkPatterns = [\n                /<a[^>]*href=\"([^\"]*)\"[^>]*data-ved=\"[^\"]*\"[^>]*>/i,\n                /<a[^>]*href=\"([^\"]*)\"[^>]*>/i\n            ];\n            // Updated snippet patterns for 2025\n            const snippetPatterns = [\n                // Current Google snippet classes\n                /<span[^>]*class=\"[^\"]*aCOpRe[^\"]*\"[^>]*>([^<]+)<\\/span>/i,\n                /<div[^>]*class=\"[^\"]*VwiC3b[^\"]*\"[^>]*>([^<]+)<\\/div>/i,\n                /<span[^>]*class=\"[^\"]*st[^\"]*\"[^>]*>([^<]+)<\\/span>/i,\n                /<div[^>]*data-content-feature=\"snippet\"[^>]*>([^<]+)<\\/div>/i,\n                /<span[^>]*data-snippet=\"[^\"]*\"[^>]*>([^<]+)<\\/span>/i,\n                // Fallback patterns\n                /<div[^>]*class=\"[^\"]*IsZvec[^\"]*\"[^>]*>([^<]+)<\\/div>/i,\n                /<span[^>]*class=\"[^\"]*hgKElc[^\"]*\"[^>]*>([^<]+)<\\/span>/i\n            ];\n            // Try each result container pattern\n            for (const containerPattern of modernResultPatterns){\n                let containerMatch;\n                while((containerMatch = containerPattern.exec(htmlContent)) !== null && results.length < 10){\n                    const containerContent = containerMatch[1];\n                    // Extract title using multiple patterns\n                    let title = '';\n                    for (const titlePattern of titlePatterns){\n                        const titleMatch = titlePattern.exec(containerContent);\n                        if (titleMatch) {\n                            title = titleMatch[1].trim();\n                            break;\n                        }\n                    }\n                    // Extract link using multiple patterns\n                    let link = '';\n                    for (const linkPattern of linkPatterns){\n                        const linkMatch = linkPattern.exec(containerContent);\n                        if (linkMatch) {\n                            link = linkMatch[1];\n                            break;\n                        }\n                    }\n                    if (title && link && link.startsWith('http') && !link.includes('google.com') && !link.includes('/search?') && !link.includes('webcache.googleusercontent.com')) {\n                        // Extract snippet using multiple patterns\n                        let snippet = '';\n                        for (const snippetPattern of snippetPatterns){\n                            const snippetMatch = snippetPattern.exec(containerContent);\n                            if (snippetMatch) {\n                                snippet = snippetMatch[1].trim();\n                                // Clean up HTML entities and normalize whitespace\n                                snippet = snippet.replace(/&[^;]+;/g, ' ').replace(/<[^>]*>/g, ' ').replace(/\\s+/g, ' ').trim();\n                                break;\n                            }\n                        }\n                        // Enhanced fallback snippet extraction\n                        if (!snippet) {\n                            // Try to extract any meaningful text from the container\n                            const cleanText = containerContent.replace(/<script[^>]*>.*?<\\/script>/gis, '').replace(/<style[^>]*>.*?<\\/style>/gis, '').replace(/<[^>]*>/g, ' ').replace(/\\s+/g, ' ').trim();\n                            const sentences = cleanText.split(/[.!?]+/).filter((s)=>s.trim().length > 20);\n                            if (sentences.length > 0) {\n                                snippet = sentences[0].trim().substring(0, 200) + '...';\n                            } else {\n                                const words = cleanText.split(' ').filter((w)=>w.length > 2);\n                                if (words.length > 10) {\n                                    snippet = words.slice(0, 25).join(' ') + '...';\n                                }\n                            }\n                        }\n                        results.push({\n                            title,\n                            link,\n                            snippet: snippet || 'No description available',\n                            searchEngine: 'google',\n                            query,\n                            timestamp: new Date().toISOString(),\n                            relevanceScore: snippet.length > 50 ? 0.9 : 0.7 // Higher score for better snippets\n                        });\n                    }\n                }\n                if (results.length > 0) break; // Stop if we found results with this pattern\n            }\n        } else {\n            // Updated Bing search result patterns for 2025\n            const bingContainerPatterns = [\n                /<li[^>]*class=\"[^\"]*b_algo[^\"]*\"[^>]*>(.*?)<\\/li>/gis,\n                /<div[^>]*class=\"[^\"]*b_algoheader[^\"]*\"[^>]*>(.*?)<\\/div>/gis\n            ];\n            const bingTitlePatterns = [\n                /<h2[^>]*><a[^>]*href=\"([^\"]*)\"[^>]*>([^<]+)<\\/a><\\/h2>/i,\n                /<a[^>]*href=\"([^\"]*)\"[^>]*><h2[^>]*>([^<]+)<\\/h2><\\/a>/i\n            ];\n            const bingSnippetPatterns = [\n                /<p[^>]*class=\"[^\"]*b_lineclamp[^\"]*\"[^>]*>([^<]+)<\\/p>/i,\n                /<div[^>]*class=\"[^\"]*b_caption[^\"]*\"[^>]*>([^<]+)<\\/div>/i,\n                /<p[^>]*>([^<]+)<\\/p>/i\n            ];\n            for (const containerPattern of bingContainerPatterns){\n                let containerMatch;\n                while((containerMatch = containerPattern.exec(htmlContent)) !== null && results.length < 10){\n                    const containerContent = containerMatch[1];\n                    let title = '', link = '';\n                    for (const titlePattern of bingTitlePatterns){\n                        const titleMatch = titlePattern.exec(containerContent);\n                        if (titleMatch) {\n                            link = titleMatch[1];\n                            title = titleMatch[2].trim();\n                            break;\n                        }\n                    }\n                    if (title && link) {\n                        let snippet = '';\n                        for (const snippetPattern of bingSnippetPatterns){\n                            const snippetMatch = snippetPattern.exec(containerContent);\n                            if (snippetMatch) {\n                                snippet = snippetMatch[1].trim().replace(/&[^;]+;/g, ' ').replace(/<[^>]*>/g, ' ').replace(/\\s+/g, ' ').trim();\n                                break;\n                            }\n                        }\n                        results.push({\n                            title,\n                            link,\n                            snippet: snippet || 'No description available',\n                            searchEngine: 'bing',\n                            query,\n                            timestamp: new Date().toISOString(),\n                            relevanceScore: snippet.length > 50 ? 0.9 : 0.7\n                        });\n                    }\n                }\n                if (results.length > 0) break;\n            }\n        }\n        // Sort results by relevance score (better snippets first)\n        results.sort((a, b)=>(b.relevanceScore || 0) - (a.relevanceScore || 0));\n        console.log(`[Browserless] Parsed ${results.length} search results with enhanced snippets for query: \"${query}\"`);\n        return results.slice(0, 8); // Return top 8 most relevant results\n    }\n    async searchAndExtract(query, searchEngine = 'google') {\n        const searchUrl = searchEngine === 'google' ? `https://www.google.com/search?q=${encodeURIComponent(query)}` : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;\n        const code = `\n      export default async function ({ page }) {\n        // Set a realistic user agent and headers\n        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');\n        await page.setExtraHTTPHeaders({\n          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',\n          'Accept-Language': 'en-US,en;q=0.5',\n          'Accept-Encoding': 'gzip, deflate',\n          'DNT': '1',\n          'Connection': 'keep-alive',\n          'Upgrade-Insecure-Requests': '1',\n        });\n\n        await page.goto(\"${searchUrl}\", { waitUntil: 'networkidle0' });\n\n        // Set up CAPTCHA solving\n        const cdp = await page.createCDPSession();\n\n        // Check for CAPTCHA and solve if found\n        let captchaFound = false;\n        cdp.on('Browserless.captchaFound', () => {\n          console.log('CAPTCHA detected on search page');\n          captchaFound = true;\n        });\n\n        // Wait a moment to see if CAPTCHA is detected\n        await new Promise(resolve => setTimeout(resolve, 2000));\n\n        if (captchaFound) {\n          console.log('Attempting to solve CAPTCHA...');\n          try {\n            const { solved, error } = await cdp.send('Browserless.solveCaptcha');\n            console.log('CAPTCHA solving result:', { solved, error });\n\n            if (solved) {\n              console.log('CAPTCHA solved successfully');\n              // Wait for page to reload after CAPTCHA solving\n              await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 10000 }).catch(() => {\n                console.log('No navigation after CAPTCHA solve, continuing...');\n              });\n            } else {\n              console.log('CAPTCHA solving failed:', error);\n            }\n          } catch (captchaError) {\n            console.log('CAPTCHA solving error:', captchaError);\n          }\n        }\n\n        // Wait for search results to load with multiple fallback selectors\n        let resultsLoaded = false;\n        const googleSelectors = ['[data-ved]', 'h3', '.g h3', '.LC20lb', '.DKV0Md', '#search h3'];\n        const bingSelectors = ['.b_algo', '.b_algo h2', 'h2 a'];\n\n        const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;\n\n        for (const selector of selectorsToTry) {\n          try {\n            await page.waitForSelector(selector, { timeout: 3000 });\n            resultsLoaded = true;\n            console.log('Found results with selector:', selector);\n            break;\n          } catch (e) {\n            console.log('Selector failed:', selector);\n            continue;\n          }\n        }\n\n        if (!resultsLoaded) {\n          // Give it one more chance with a longer timeout on the most common selector\n          try {\n            await page.waitForSelector('${searchEngine === 'google' ? 'h3' : '.b_algo'}', { timeout: 5000 });\n          } catch (e) {\n            console.log('All selectors failed, proceeding anyway...');\n          }\n        }\n\n        const results = await page.evaluate(() => {\n          console.log('Starting search results extraction...');\n\n          // Try multiple selectors for extracting results\n          const googleSelectors = [\n            '[data-ved] h3',\n            'h3',\n            '.g h3',\n            '.LC20lb',\n            '.DKV0Md',\n            '#search h3',\n            '.yuRUbf h3',\n            'a h3',\n            '[role=\"heading\"]'\n          ];\n          const bingSelectors = ['.b_algo h2', '.b_algo h2 a', 'h2 a'];\n\n          const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;\n          let elements = [];\n          let usedSelector = '';\n\n          // Debug: Log page content\n          console.log('Page title:', document.title);\n          console.log('Page URL:', window.location.href);\n          console.log('Page body preview:', document.body.innerText.substring(0, 500));\n\n          for (const selector of selectorsToTry) {\n            elements = document.querySelectorAll(selector);\n            console.log('Trying selector:', selector, 'found:', elements.length, 'elements');\n            if (elements.length > 0) {\n              usedSelector = selector;\n              console.log('Found', elements.length, 'results with selector:', selector);\n              break;\n            }\n          }\n\n          // If no results found, try a more generic approach\n          if (elements.length === 0) {\n            console.log('No results with specific selectors, trying generic approach...');\n            // Try to find any links that look like search results\n            const allLinks = document.querySelectorAll('a[href*=\"/url?\"]');\n            console.log('Found', allLinks.length, 'Google result links');\n\n            if (allLinks.length > 0) {\n              elements = Array.from(allLinks).map(link => {\n                const h3 = link.querySelector('h3');\n                return h3 || link;\n              }).filter(el => el && el.textContent?.trim());\n              usedSelector = 'a[href*=\"/url?\"] h3 (fallback)';\n              console.log('Using fallback approach, found', elements.length, 'elements');\n            }\n          }\n\n          const extractedResults = Array.from(elements).slice(0, 5).map(el => {\n            const title = el.textContent?.trim() || '';\n            let link = '';\n\n            // Try to get the link\n            if (el.href) {\n              link = el.href;\n            } else {\n              const closestLink = el.closest('a');\n              if (closestLink) {\n                link = closestLink.href;\n              }\n            }\n\n            return { title, link };\n          }).filter(item => item.title && item.link);\n\n          console.log('Final results:', extractedResults.length, 'items using selector:', usedSelector);\n\n          return {\n            results: extractedResults,\n            debug: {\n              pageTitle: document.title,\n              pageUrl: window.location.href,\n              totalElements: elements.length,\n              usedSelector: usedSelector || 'none',\n              extractedCount: extractedResults.length\n            }\n          };\n        });\n\n        return {\n          data: {\n            query: \"${query}\",\n            searchEngine: \"${searchEngine}\",\n            results: results.results,\n            timestamp: new Date().toISOString(),\n            debug: results.debug\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code, null, {\n            timeout: 60000 // Increase timeout for CAPTCHA solving\n        });\n    }\n    async takeScreenshot(url, options) {\n        const fullPage = options?.fullPage ?? false;\n        const selector = options?.selector || '';\n        const quality = options?.quality || 80;\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        let screenshot;\n        if (\"${selector}\") {\n          // Screenshot specific element\n          const element = await page.waitForSelector(\"${selector}\", { timeout: 10000 });\n          screenshot = await element.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n        } else {\n          // Screenshot full page or viewport\n          screenshot = await page.screenshot({\n            encoding: 'base64',\n            fullPage: ${fullPage},\n            type: 'png',\n            quality: ${quality}\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            screenshot: screenshot,\n            selector: \"${selector}\",\n            fullPage: ${fullPage},\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Advanced form filling with intelligent field detection\n   */ async fillForm(url, formData, options) {\n        const submitAfterFill = options?.submitAfterFill ?? false;\n        const waitForNavigation = options?.waitForNavigation ?? false;\n        const formSelector = options?.formSelector || 'form';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const formData = ${JSON.stringify(formData)};\n        const results = [];\n\n        // Wait for form to be present\n        await page.waitForSelector(\"${formSelector}\", { timeout: 10000 });\n\n        // Fill each field intelligently\n        for (const [fieldName, value] of Object.entries(formData)) {\n          try {\n            // Try multiple selector strategies\n            const selectors = [\n              \\`input[name=\"\\${fieldName}\"]\\`,\n              \\`input[id=\"\\${fieldName}\"]\\`,\n              \\`textarea[name=\"\\${fieldName}\"]\\`,\n              \\`select[name=\"\\${fieldName}\"]\\`,\n              \\`input[placeholder*=\"\\${fieldName}\"]\\`,\n              \\`input[aria-label*=\"\\${fieldName}\"]\\`,\n              \\`[data-testid=\"\\${fieldName}\"]\\`\n            ];\n\n            let filled = false;\n            for (const selector of selectors) {\n              const elements = await page.$$(selector);\n              if (elements.length > 0) {\n                const element = elements[0];\n                const tagName = await element.evaluate(el => el.tagName.toLowerCase());\n\n                if (tagName === 'select') {\n                  await element.selectOption(value.toString());\n                } else if (tagName === 'input') {\n                  const inputType = await element.getAttribute('type');\n                  if (inputType === 'checkbox' || inputType === 'radio') {\n                    if (value) await element.check();\n                  } else {\n                    await element.fill(value.toString());\n                  }\n                } else {\n                  await element.fill(value.toString());\n                }\n\n                results.push({\n                  field: fieldName,\n                  selector: selector,\n                  value: value,\n                  success: true\n                });\n                filled = true;\n                break;\n              }\n            }\n\n            if (!filled) {\n              results.push({\n                field: fieldName,\n                value: value,\n                success: false,\n                error: 'Field not found'\n              });\n            }\n          } catch (error) {\n            results.push({\n              field: fieldName,\n              value: value,\n              success: false,\n              error: error.message\n            });\n          }\n        }\n\n        let submitResult = null;\n        if (${submitAfterFill}) {\n          try {\n            const submitButton = await page.$('input[type=\"submit\"], button[type=\"submit\"], button:has-text(\"Submit\")');\n            if (submitButton) {\n              ${waitForNavigation ? 'await Promise.all([page.waitForNavigation(), submitButton.click()]);' : 'await submitButton.click();'}\n              submitResult = { success: true, message: 'Form submitted successfully' };\n            } else {\n              submitResult = { success: false, error: 'Submit button not found' };\n            }\n          } catch (error) {\n            submitResult = { success: false, error: error.message };\n          }\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            formFillResults: results,\n            submitResult: submitResult,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * CAPTCHA solving with multiple strategies\n   */ async solveCaptcha(url, captchaType = 'recaptcha') {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const captchaType = \"${captchaType}\";\n        let result = { success: false, type: captchaType };\n\n        try {\n          if (captchaType === 'recaptcha') {\n            // Look for reCAPTCHA\n            const recaptcha = await page.$('.g-recaptcha, [data-sitekey]');\n            if (recaptcha) {\n              // For now, we'll detect and report the presence\n              // In production, integrate with 2captcha or similar service\n              const sitekey = await recaptcha.getAttribute('data-sitekey');\n              result = {\n                success: false,\n                type: 'recaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'reCAPTCHA detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'hcaptcha') {\n            // Look for hCaptcha\n            const hcaptcha = await page.$('.h-captcha, [data-hcaptcha-sitekey]');\n            if (hcaptcha) {\n              const sitekey = await hcaptcha.getAttribute('data-hcaptcha-sitekey');\n              result = {\n                success: false,\n                type: 'hcaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'hCaptcha detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'text') {\n            // Look for text-based CAPTCHA\n            const textCaptcha = await page.$('img[src*=\"captcha\"], img[alt*=\"captcha\"], .captcha-image');\n            if (textCaptcha) {\n              result = {\n                success: false,\n                type: 'text',\n                detected: true,\n                message: 'Text CAPTCHA detected but solving not implemented yet'\n              };\n            }\n          }\n\n          // If no CAPTCHA detected\n          if (!result.detected) {\n            result = {\n              success: true,\n              type: captchaType,\n              detected: false,\n              message: 'No CAPTCHA detected on page'\n            };\n          }\n        } catch (error) {\n          result = {\n            success: false,\n            type: captchaType,\n            error: error.message\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            captchaResult: result,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Execute custom JavaScript with advanced capabilities\n   */ async executeAdvancedScript(url, script, options) {\n        const waitForSelector = options?.waitForSelector || '';\n        const timeout = options?.timeout || 30000;\n        const returnType = options?.returnType || 'json';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        ${waitForSelector ? `await page.waitForSelector(\"${waitForSelector}\", { timeout: ${timeout} });` : ''}\n\n        // Execute custom script\n        const scriptResult = await page.evaluate(() => {\n          ${script}\n        });\n\n        let finalResult = scriptResult;\n\n        if (\"${returnType}\" === 'screenshot') {\n          const screenshot = await page.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n          finalResult = {\n            scriptResult: scriptResult,\n            screenshot: screenshot\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            result: finalResult,\n            returnType: \"${returnType}\",\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Smart content extraction with multiple strategies\n   */ async smartExtract(url, extractionGoals) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const goals = ${JSON.stringify(extractionGoals)};\n        const results = {};\n\n        // Common extraction patterns\n        const extractors = {\n          prices: () => {\n            const priceSelectors = [\n              '[class*=\"price\"]', '[id*=\"price\"]', '.cost', '.amount',\n              '[data-testid*=\"price\"]', '.currency', '[class*=\"dollar\"]'\n            ];\n            const prices = [];\n            priceSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const text = el.textContent?.trim();\n                if (text && /[$£€¥₹]|\\\\d+\\\\.\\\\d{2}/.test(text)) {\n                  prices.push({\n                    text: text,\n                    selector: selector,\n                    element: el.tagName\n                  });\n                }\n              });\n            });\n            return prices;\n          },\n\n          contact: () => {\n            const contactSelectors = [\n              '[href^=\"mailto:\"]', '[href^=\"tel:\"]', '.contact', '.email', '.phone'\n            ];\n            const contacts = [];\n            contactSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                contacts.push({\n                  text: el.textContent?.trim(),\n                  href: el.getAttribute('href'),\n                  type: el.getAttribute('href')?.startsWith('mailto:') ? 'email' : 'phone'\n                });\n              });\n            });\n            return contacts;\n          },\n\n          products: () => {\n            const productSelectors = [\n              '.product', '[class*=\"product\"]', '.item', '[data-testid*=\"product\"]'\n            ];\n            const products = [];\n            productSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const title = el.querySelector('h1, h2, h3, .title, [class*=\"title\"]')?.textContent?.trim();\n                const price = el.querySelector('[class*=\"price\"], .cost')?.textContent?.trim();\n                const image = el.querySelector('img')?.src;\n                if (title) {\n                  products.push({ title, price, image });\n                }\n              });\n            });\n            return products;\n          },\n\n          text: () => {\n            // Extract main content\n            const contentSelectors = ['main', 'article', '.content', '#content', '.post'];\n            let content = '';\n            for (const selector of contentSelectors) {\n              const el = document.querySelector(selector);\n              if (el) {\n                content = el.textContent?.trim() || '';\n                break;\n              }\n            }\n            if (!content) {\n              content = document.body.textContent?.trim() || '';\n            }\n            return content.substring(0, 5000); // Limit to 5000 chars\n          },\n\n          links: () => {\n            const links = [];\n            document.querySelectorAll('a[href]').forEach(el => {\n              const href = el.getAttribute('href');\n              const text = el.textContent?.trim();\n              if (href && text && !href.startsWith('#')) {\n                links.push({\n                  url: new URL(href, window.location.href).href,\n                  text: text\n                });\n              }\n            });\n            return links.slice(0, 50); // Limit to 50 links\n          }\n        };\n\n        // Execute extractors based on goals\n        goals.forEach(goal => {\n          const goalLower = goal.toLowerCase();\n          if (goalLower.includes('price') || goalLower.includes('cost')) {\n            results.prices = extractors.prices();\n          }\n          if (goalLower.includes('contact') || goalLower.includes('email') || goalLower.includes('phone')) {\n            results.contact = extractors.contact();\n          }\n          if (goalLower.includes('product') || goalLower.includes('item')) {\n            results.products = extractors.products();\n          }\n          if (goalLower.includes('text') || goalLower.includes('content')) {\n            results.text = extractors.text();\n          }\n          if (goalLower.includes('link') || goalLower.includes('url')) {\n            results.links = extractors.links();\n          }\n        });\n\n        // If no specific goals, extract everything\n        if (goals.length === 0) {\n          Object.keys(extractors).forEach(key => {\n            results[key] = extractors[key]();\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            extractionGoals: goals,\n            results: results,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    // Get service statistics\n    getStats() {\n        return {\n            totalKeys: this.apiKeys.length,\n            healthyKeys: this.getHealthyKeys().length,\n            keyUsage: Object.fromEntries(this.keyUsageCount),\n            keyErrors: Object.fromEntries(this.keyErrors)\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowserlessService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browserless.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/browsing/BrowsingExecutionService.ts":
/*!******************************************************!*\
  !*** ./src/lib/browsing/BrowsingExecutionService.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BrowsingExecutionService: () => (/* binding */ BrowsingExecutionService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_browserless__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/browserless */ \"(rsc)/./src/lib/browserless.ts\");\n/* harmony import */ var _SmartBrowsingExecutor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SmartBrowsingExecutor */ \"(rsc)/./src/lib/browsing/SmartBrowsingExecutor.ts\");\n// Browsing Execution Service - Handles browsing model selection with fallback support\n// Integrates with BrowserlessService for actual web browsing\n\n\nclass BrowsingExecutionService {\n    constructor(){\n        this.browserlessService = _lib_browserless__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance();\n        this.smartBrowsingExecutor = _SmartBrowsingExecutor__WEBPACK_IMPORTED_MODULE_1__.SmartBrowsingExecutor.getInstance();\n    }\n    static getInstance() {\n        if (!BrowsingExecutionService.instance) {\n            BrowsingExecutionService.instance = new BrowsingExecutionService();\n        }\n        return BrowsingExecutionService.instance;\n    }\n    /**\n   * Execute browsing with model fallback support\n   * Now uses SmartBrowsingExecutor for intelligent, plan-based browsing\n   */ async executeBrowsing(query, browsingConfig, browsingType = 'search', refinedQuery, useSmartBrowsing = true, progressCallback) {\n        try {\n            if (!browsingConfig.browsing_enabled) {\n                return {\n                    success: false,\n                    error: 'Browsing is not enabled for this configuration'\n                };\n            }\n            if (!browsingConfig.browsing_models || browsingConfig.browsing_models.length === 0) {\n                return {\n                    success: false,\n                    error: 'No browsing models configured'\n                };\n            }\n            console.log(`[Browsing Execution] Starting ${useSmartBrowsing ? 'SMART' : 'SIMPLE'} browsing`);\n            console.log(`[Browsing Execution] Query: \"${query}\", Type: ${browsingType}`);\n            // Use Smart Browsing for complex tasks\n            if (useSmartBrowsing) {\n                console.log(`[Browsing Execution] 🧠 Using Smart Browsing Executor`);\n                const smartResult = await this.smartBrowsingExecutor.executeSmartBrowsing(refinedQuery || query, browsingConfig, browsingType, progressCallback);\n                if (smartResult.success) {\n                    return {\n                        success: true,\n                        content: smartResult.content,\n                        modelUsed: browsingConfig.browsing_models[0]?.model || 'smart-browsing',\n                        providerUsed: browsingConfig.browsing_models[0]?.provider || 'smart-browsing',\n                        browsingData: smartResult.plan\n                    };\n                } else {\n                    // Check if we should fallback due to network issues\n                    if (smartResult.shouldFallback) {\n                        console.log(`[Browsing Execution] 🌐 Network issue detected, falling back to simple browsing: ${smartResult.error}`);\n                    } else {\n                        console.log(`[Browsing Execution] Smart browsing failed, falling back to simple browsing: ${smartResult.error}`);\n                    }\n                // Fall back to simple browsing\n                }\n            }\n            // Fallback to simple browsing (original logic)\n            console.log(`[Browsing Execution] 🔄 Using Simple Browsing (fallback)`);\n            return await this.executeSimpleBrowsing(query, browsingConfig, browsingType, refinedQuery);\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Browsing Execution] Fatal error:', errorMessage);\n            return {\n                success: false,\n                error: `Browsing execution failed: ${errorMessage}`\n            };\n        }\n    }\n    /**\n   * Execute simple browsing (original logic) as fallback\n   */ async executeSimpleBrowsing(query, browsingConfig, browsingType = 'search', refinedQuery) {\n        // Sort models by order for fallback\n        const sortedModels = [\n            ...browsingConfig.browsing_models\n        ].sort((a, b)=>a.order - b.order);\n        console.log(`[Simple Browsing] Starting with ${sortedModels.length} models configured`);\n        let lastError = null;\n        // Try each model in order (strict fallback pattern)\n        for (const model of sortedModels){\n            try {\n                console.log(`[Simple Browsing] Attempting with ${model.provider}/${model.model}`);\n                // First, perform the web browsing\n                const browsingResult = await this.performWebBrowsing(refinedQuery || query, browsingType);\n                if (!browsingResult.success) {\n                    throw new Error(browsingResult.error || 'Browsing failed');\n                }\n                console.log(`[Simple Browsing] ✅ Web browsing successful, got ${JSON.stringify(browsingResult.data).length} characters of data`);\n                // Then, use the AI model to process the browsing results\n                const aiResult = await this.processWithAI(query, browsingResult.data, model, browsingType);\n                if (aiResult.success) {\n                    console.log(`[Simple Browsing] ✅ Success with ${model.provider}/${model.model}`);\n                    return {\n                        success: true,\n                        content: aiResult.content,\n                        modelUsed: model.model,\n                        providerUsed: model.provider,\n                        browsingData: browsingResult.data\n                    };\n                } else {\n                    throw new Error(aiResult.error || 'AI processing failed');\n                }\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n                lastError = errorMessage;\n                console.log(`[Simple Browsing] ❌ Failed with ${model.provider}/${model.model}: ${errorMessage}`);\n                continue;\n            }\n        }\n        // If we get here, all models failed\n        return {\n            success: false,\n            error: `All browsing models failed. Last error: ${lastError}`\n        };\n    }\n    /**\n   * Perform web browsing using BrowserlessService\n   */ async performWebBrowsing(query, browsingType) {\n        try {\n            let result;\n            switch(browsingType){\n                case 'search':\n                    // Use search functionality\n                    result = await this.browserlessService.searchAndExtractUnblocked(query);\n                    break;\n                case 'navigate':\n                    // Try to extract URL from query or use as-is\n                    const urlMatch = query.match(/https?:\\/\\/[^\\s]+/);\n                    const url = urlMatch ? urlMatch[0] : query;\n                    result = await this.browserlessService.navigateAndExtract(url);\n                    break;\n                case 'extract':\n                    // Similar to navigate but with specific extraction\n                    const extractUrl = query.match(/https?:\\/\\/[^\\s]+/)?.[0] || query;\n                    result = await this.browserlessService.navigateAndExtract(extractUrl);\n                    break;\n                default:\n                    // Default to search\n                    result = await this.browserlessService.searchAndExtractUnblocked(query);\n            }\n            if (result && result.data) {\n                console.log(`[Browsing Execution] ✅ Web browsing successful, got ${JSON.stringify(result.data).length} characters of data`);\n                return {\n                    success: true,\n                    data: result.data\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'No data returned from browsing'\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Browsing Execution] Web browsing failed:', errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Process browsing results with AI model\n   */ async processWithAI(originalQuery, browsingData, model, browsingType) {\n        try {\n            const prompt = this.buildProcessingPrompt(originalQuery, browsingData, browsingType);\n            // Call the appropriate AI provider\n            const response = await this.callAIProvider(prompt, model);\n            if (response && response.content) {\n                return {\n                    success: true,\n                    content: response.content\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'No content returned from AI model'\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Build processing prompt for AI model\n   */ buildProcessingPrompt(originalQuery, browsingData, browsingType) {\n        const dataStr = JSON.stringify(browsingData, null, 2);\n        return `You are an AI assistant that processes web browsing results to answer user queries.\n\nUSER QUERY: \"${originalQuery}\"\nBROWSING TYPE: ${browsingType}\n\nWEB BROWSING RESULTS:\n${dataStr}\n\nINSTRUCTIONS:\n1. Analyze the browsing results carefully\n2. Extract relevant information that answers the user's query\n3. Provide a comprehensive, well-structured response\n4. If the browsing results don't contain relevant information, say so clearly\n5. Include specific details, numbers, dates, and facts from the browsing results\n6. Organize the information in a clear, readable format\n7. Cite sources when possible (URLs, website names, etc.)\n\nPlease provide a helpful response based on the browsing results:`;\n    }\n    /**\n   * Get the correct model ID for API calls (following RouKey's pattern)\n   * OpenRouter keeps full model ID, other providers strip the prefix\n   */ getEffectiveModelId(model) {\n        // For OpenRouter, return the full model ID\n        if (model.provider.toLowerCase() === 'openrouter') {\n            return model.model;\n        }\n        // For other providers, extract the model name after the prefix\n        const parts = model.model.split('/');\n        return parts.length > 1 ? parts[parts.length - 1] : model.model;\n    }\n    /**\n   * Call AI provider based on model configuration\n   */ async callAIProvider(prompt, model) {\n        try {\n            const messages = [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ];\n            // Get the effective model ID following RouKey's pattern\n            const effectiveModelId = this.getEffectiveModelId(model);\n            let apiUrl;\n            let headers;\n            let body;\n            // Configure API call based on provider\n            switch(model.provider){\n                case 'openai':\n                    apiUrl = 'https://api.openai.com/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'anthropic':\n                    apiUrl = 'https://api.anthropic.com/v1/messages';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'x-api-key': model.api_key,\n                        'anthropic-version': '2023-06-01'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'google':\n                    apiUrl = 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'openrouter':\n                    apiUrl = 'https://openrouter.ai/api/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`,\n                        'HTTP-Referer': 'https://roukey.online',\n                        'X-Title': 'RouKey'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                default:\n                    throw new Error(`Unsupported provider: ${model.provider}`);\n            }\n            console.log(`[Browsing AI] Calling ${model.provider} API with model ${effectiveModelId} (original: ${model.model})...`);\n            const response = await fetch(apiUrl, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify(body),\n                signal: AbortSignal.timeout(30000) // 30s timeout\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(`[Browsing AI] API error: ${response.status} - ${errorText}`);\n                throw new Error(`API error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            console.log(`[Browsing AI] Raw response:`, JSON.stringify(result, null, 2));\n            // Extract content based on provider response format\n            let content;\n            if (model.provider === 'anthropic') {\n                content = result.content?.[0]?.text;\n            } else {\n                // For OpenAI-compatible APIs (including Google's OpenAI-compatible endpoint)\n                content = result.choices?.[0]?.message?.content;\n            }\n            console.log(`[Browsing AI] Extracted content length: ${content?.length || 0}`);\n            if (!content || content.trim().length === 0) {\n                console.error(`[Browsing AI] No content extracted from response. Full response:`, result);\n                return {\n                    error: 'No content returned from AI model - empty response'\n                };\n            }\n            return {\n                content\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error(`[Browsing AI] Error calling AI provider:`, errorMessage);\n            return {\n                error: errorMessage\n            };\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowsingExecutionService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browsing/BrowsingExecutionService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/browsing/SmartBrowsingExecutor.ts":
/*!***************************************************!*\
  !*** ./src/lib/browsing/SmartBrowsingExecutor.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartBrowsingExecutor: () => (/* binding */ SmartBrowsingExecutor)\n/* harmony export */ });\n/* harmony import */ var _lib_browserless__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/browserless */ \"(rsc)/./src/lib/browserless.ts\");\n// Smart Browsing Executor - Intelligent plan-based browsing with todo list management\n// Handles complex browsing tasks by creating plans, executing subtasks, and updating progress\n\nclass SmartBrowsingExecutor {\n    constructor(){\n        this.activePlans = new Map();\n        this.browserlessService = _lib_browserless__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance();\n    }\n    static getInstance() {\n        if (!SmartBrowsingExecutor.instance) {\n            SmartBrowsingExecutor.instance = new SmartBrowsingExecutor();\n        }\n        return SmartBrowsingExecutor.instance;\n    }\n    /**\n   * Execute smart browsing with planning and todo list management\n   */ async executeSmartBrowsing(query, browsingConfig, browsingType = 'search', progressCallback) {\n        try {\n            console.log(`[Smart Browsing] 🎯 Starting intelligent browsing for: \"${query}\"`);\n            // Step 1: Create a browsing plan\n            const plan = await this.createBrowsingPlan(query, browsingType, browsingConfig);\n            this.activePlans.set(plan.id, plan);\n            console.log(`[Smart Browsing] 📋 Created plan with ${plan.subtasks.length} subtasks`);\n            this.logPlan(plan);\n            // Notify plan creation\n            progressCallback?.onPlanCreated?.(plan);\n            // Step 2: Execute the plan\n            const result = await this.executePlan(plan, browsingConfig, progressCallback);\n            if (result.success) {\n                console.log(`[Smart Browsing] ✅ Plan completed successfully`);\n                return {\n                    success: true,\n                    content: result.content,\n                    plan: plan\n                };\n            } else {\n                console.log(`[Smart Browsing] ❌ Plan failed: ${result.error}`);\n                // Check if this is a network connectivity issue\n                if (this.isNetworkError(result.error)) {\n                    console.log(`[Smart Browsing] 🌐 Network connectivity issue detected, recommending fallback to simple browsing`);\n                    return {\n                        success: false,\n                        error: `Network connectivity issue: ${result.error}. Falling back to simple browsing.`,\n                        plan: plan,\n                        shouldFallback: true\n                    };\n                }\n                return {\n                    success: false,\n                    error: result.error,\n                    plan: plan\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Smart Browsing] Fatal error:', errorMessage);\n            // Check if this is a network connectivity issue\n            if (this.isNetworkError(errorMessage)) {\n                console.log(`[Smart Browsing] 🌐 Network connectivity issue detected, recommending fallback to simple browsing`);\n                return {\n                    success: false,\n                    error: `Network connectivity issue: ${errorMessage}. Falling back to simple browsing.`,\n                    shouldFallback: true\n                };\n            }\n            return {\n                success: false,\n                error: `Smart browsing failed: ${errorMessage}`\n            };\n        }\n    }\n    /**\n   * Check if an error is related to network connectivity\n   */ isNetworkError(errorMessage) {\n        const networkErrorPatterns = [\n            'fetch failed',\n            'ECONNRESET',\n            'ENOTFOUND',\n            'ETIMEDOUT',\n            'ECONNREFUSED',\n            'Network request failed',\n            'Connection timeout',\n            'DNS resolution failed'\n        ];\n        return networkErrorPatterns.some((pattern)=>errorMessage.toLowerCase().includes(pattern.toLowerCase()));\n    }\n    /**\n   * Create an intelligent browsing plan based on the query\n   */ async createBrowsingPlan(query, browsingType, browsingConfig) {\n        const planId = `plan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        // Use AI to create a smart plan\n        const planningResult = await this.generatePlanWithAI(query, browsingType, browsingConfig);\n        const plan = {\n            id: planId,\n            originalQuery: query,\n            goal: planningResult.goal || `Find comprehensive information about: ${query}`,\n            subtasks: planningResult.subtasks || this.createFallbackPlan(query, browsingType),\n            status: 'planning',\n            progress: 0,\n            gatheredData: {},\n            visitedUrls: [],\n            searchQueries: [],\n            completedSubtasks: [],\n            failedSubtasks: [],\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        return plan;\n    }\n    /**\n   * Generate a browsing plan using AI\n   */ async generatePlanWithAI(query, browsingType, browsingConfig) {\n        try {\n            const model = browsingConfig.browsing_models[0]; // Use first available model for planning\n            if (!model) {\n                throw new Error('No browsing models available for planning');\n            }\n            const planningPrompt = this.buildPlanningPrompt(query, browsingType);\n            const aiResult = await this.callAIForPlanning(planningPrompt, model);\n            if (aiResult.success && aiResult.content) {\n                return this.parsePlanFromAI(aiResult.content, query);\n            } else {\n                console.warn('[Smart Browsing] AI planning failed, using fallback plan');\n                return {\n                    goal: `Find information about: ${query}`,\n                    subtasks: this.createFallbackPlan(query, browsingType)\n                };\n            }\n        } catch (error) {\n            console.warn('[Smart Browsing] AI planning error, using fallback:', error);\n            return {\n                goal: `Find information about: ${query}`,\n                subtasks: this.createFallbackPlan(query, browsingType)\n            };\n        }\n    }\n    /**\n   * Build a comprehensive planning prompt for AI\n   */ buildPlanningPrompt(query, browsingType) {\n        // Get current date and time for context\n        const now = new Date();\n        const currentDateTime = now.toLocaleString('en-US', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit',\n            timeZoneName: 'short'\n        });\n        const currentYear = now.getFullYear();\n        const currentMonth = now.toLocaleString('en-US', {\n            month: 'long'\n        });\n        return `You are an expert web browsing strategist. Create a detailed browsing plan to thoroughly research this query: \"${query}\"\n\nCURRENT DATE & TIME: ${currentDateTime}\nCURRENT YEAR: ${currentYear}\nCURRENT MONTH: ${currentMonth}\n\nBROWSING TYPE: ${browsingType}\n\nIMPORTANT: When generating search terms and subtasks, consider the current date and time context:\n- For recent events, include \"${currentYear}\" in search terms\n- For current trends, use \"latest\", \"recent\", \"${currentMonth} ${currentYear}\"\n- For news queries, prioritize recent timeframes\n- For technology/AI topics, include current year for latest developments\n- For market/business queries, focus on current and recent data\n\nCreate a JSON response with this structure:\n{\n  \"goal\": \"Clear statement of what we want to achieve\",\n  \"subtasks\": [\n    {\n      \"id\": \"unique_id\",\n      \"type\": \"search|navigate|extract|analyze\",\n      \"description\": \"What this subtask does\",\n      \"query\": \"Specific search query or URL\",\n      \"priority\": 1-10,\n      \"searchTerms\": [\"alternative\", \"search\", \"terms\"],\n      \"expectedInfo\": \"What information we expect to find\"\n    }\n  ]\n}\n\nGUIDELINES:\n1. Start with broad searches, then get more specific\n2. Use multiple search strategies and terms with temporal context\n3. Include fact-checking and verification steps\n4. Plan for 3-7 subtasks maximum\n5. Make search terms diverse, comprehensive, and time-aware\n6. Consider different angles and perspectives\n7. Include analysis steps to synthesize information\n8. For \"navigate\" tasks, only use if you have specific URLs (https://...)\n9. Most tasks should be \"search\" type for better reliability\n10. ALWAYS include temporal keywords when relevant:\n    - For news: \"latest news\", \"recent updates\", \"${currentMonth} ${currentYear}\"\n    - For trends: \"current trends\", \"${currentYear} trends\"\n    - For technology: \"latest developments\", \"${currentYear} updates\"\n    - For data/statistics: \"current data\", \"recent statistics\", \"${currentYear} data\"\n\nCreate a smart, thorough, and temporally-aware plan:`;\n    }\n    /**\n   * Enhance query with temporal context for better search results\n   */ enhanceQueryWithTemporal(query, temporalKeywords) {\n        const currentYear = new Date().getFullYear();\n        const currentMonth = new Date().toLocaleString('en-US', {\n            month: 'long'\n        });\n        // Detect if query already has temporal context\n        const hasTemporalContext = /\\b(latest|recent|current|new|today|\\d{4}|now)\\b/i.test(query);\n        return {\n            primary: query,\n            temporal: hasTemporalContext ? query : `${query} ${currentYear} latest`,\n            alternatives: [\n                query,\n                `${query} information`,\n                `${query} details`,\n                hasTemporalContext ? `${query} overview` : `${query} ${currentYear}`\n            ],\n            recentTerms: [\n                `${query} recent`,\n                `${query} latest news`,\n                `${query} ${currentMonth} ${currentYear}`,\n                `${query} current trends`\n            ]\n        };\n    }\n    /**\n   * Create a fallback plan when AI planning fails\n   */ createFallbackPlan(query, browsingType) {\n        const baseId = Date.now();\n        const currentYear = new Date().getFullYear();\n        const currentMonth = new Date().toLocaleString('en-US', {\n            month: 'long'\n        });\n        // Add temporal context to search terms\n        const temporalKeywords = [\n            `${currentYear}`,\n            `latest`,\n            `recent`,\n            `${currentMonth} ${currentYear}`\n        ];\n        const enhancedQuery = this.enhanceQueryWithTemporal(query, temporalKeywords);\n        return [\n            {\n                id: `search_${baseId}_1`,\n                type: 'search',\n                description: 'Primary search for main topic',\n                query: enhancedQuery.primary,\n                status: 'pending',\n                priority: 10,\n                attempts: 0,\n                maxAttempts: 3,\n                searchTerms: enhancedQuery.alternatives,\n                expectedInfo: 'General information about the topic'\n            },\n            {\n                id: `search_${baseId}_2`,\n                type: 'search',\n                description: 'Secondary search with temporal context',\n                query: enhancedQuery.temporal,\n                status: 'pending',\n                priority: 8,\n                attempts: 0,\n                maxAttempts: 3,\n                searchTerms: enhancedQuery.recentTerms,\n                expectedInfo: 'Recent developments and current information'\n            },\n            {\n                id: `analyze_${baseId}`,\n                type: 'analyze',\n                description: 'Analyze and synthesize gathered information',\n                query: 'synthesize findings',\n                status: 'pending',\n                priority: 5,\n                attempts: 0,\n                maxAttempts: 1,\n                dependencies: [\n                    `search_${baseId}_1`,\n                    `search_${baseId}_2`\n                ],\n                expectedInfo: 'Comprehensive summary of findings'\n            }\n        ];\n    }\n    /**\n   * Execute the browsing plan step by step\n   */ async executePlan(plan, browsingConfig, progressCallback) {\n        plan.status = 'executing';\n        plan.updatedAt = new Date().toISOString();\n        console.log(`[Smart Browsing] 🚀 Starting plan execution`);\n        try {\n            // Execute subtasks in priority order, respecting dependencies\n            while(this.hasRemainingTasks(plan)){\n                const nextTask = this.getNextExecutableTask(plan);\n                if (!nextTask) {\n                    console.log(`[Smart Browsing] ⏸️ No executable tasks remaining, checking if plan is complete`);\n                    break;\n                }\n                console.log(`[Smart Browsing] 🔄 Executing subtask: ${nextTask.description}`);\n                plan.currentSubtask = nextTask.id;\n                nextTask.status = 'in_progress';\n                // Enhance the subtask with context-aware query building\n                const enhancedTask = this.enhanceSubtaskQuery(nextTask, plan);\n                // Update the task in the plan with enhanced version\n                const taskIndex = plan.subtasks.findIndex((task)=>task.id === nextTask.id);\n                if (taskIndex !== -1) {\n                    plan.subtasks[taskIndex] = enhancedTask;\n                }\n                // Notify task started\n                progressCallback?.onTaskStarted?.(enhancedTask, plan);\n                progressCallback?.onStatusUpdate?.(`Executing: ${enhancedTask.description}`, plan);\n                const taskResult = await this.executeSubtask(enhancedTask, plan, browsingConfig);\n                if (taskResult.success) {\n                    nextTask.status = 'completed';\n                    nextTask.result = taskResult.data;\n                    plan.completedSubtasks.push(nextTask.id);\n                    // Store result in plan's gathered data for context passing\n                    plan.gatheredData[nextTask.id] = {\n                        taskType: nextTask.type,\n                        description: nextTask.description,\n                        query: nextTask.query,\n                        result: taskResult.data,\n                        completedAt: new Date().toISOString()\n                    };\n                    console.log(`[Smart Browsing] ✅ Subtask completed: ${nextTask.description}`);\n                    console.log(`[Smart Browsing] 📊 Stored context data for future subtasks`);\n                    // Notify task completed\n                    progressCallback?.onTaskCompleted?.(nextTask, plan);\n                } else {\n                    nextTask.attempts++;\n                    nextTask.error = taskResult.error;\n                    if (nextTask.attempts >= nextTask.maxAttempts) {\n                        nextTask.status = 'failed';\n                        plan.failedSubtasks.push(nextTask.id);\n                        console.log(`[Smart Browsing] ❌ Subtask failed permanently: ${nextTask.description}`);\n                        // Notify task failed\n                        progressCallback?.onTaskFailed?.(nextTask, plan);\n                    } else {\n                        nextTask.status = 'pending';\n                        console.log(`[Smart Browsing] 🔄 Subtask failed, will retry (${nextTask.attempts}/${nextTask.maxAttempts}): ${nextTask.description}`);\n                    }\n                }\n                // Update progress\n                plan.progress = this.calculateProgress(plan);\n                progressCallback?.onProgressUpdate?.(plan.progress, plan);\n                plan.updatedAt = new Date().toISOString();\n                this.logProgress(plan);\n            }\n            // Generate final result\n            const finalResult = await this.synthesizeFinalResult(plan, browsingConfig);\n            plan.finalResult = finalResult;\n            plan.status = 'completed';\n            plan.progress = 100;\n            // Notify plan completed\n            progressCallback?.onPlanCompleted?.(plan);\n            progressCallback?.onStatusUpdate?.('Browsing completed successfully!', plan);\n            return {\n                success: true,\n                content: finalResult\n            };\n        } catch (error) {\n            plan.status = 'failed';\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Smart Browsing] Plan execution failed:', errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Check if there are remaining tasks to execute\n   */ hasRemainingTasks(plan) {\n        return plan.subtasks.some((task)=>task.status === 'pending' || task.status === 'in_progress');\n    }\n    /**\n   * Get the next executable task (highest priority, dependencies met)\n   */ getNextExecutableTask(plan) {\n        const executableTasks = plan.subtasks.filter((task)=>{\n            if (task.status !== 'pending') return false;\n            // Check if dependencies are met\n            if (task.dependencies && task.dependencies.length > 0) {\n                return task.dependencies.every((depId)=>plan.completedSubtasks.includes(depId));\n            }\n            return true;\n        });\n        // Sort by priority (highest first)\n        executableTasks.sort((a, b)=>b.priority - a.priority);\n        return executableTasks[0] || null;\n    }\n    /**\n   * Execute a single subtask\n   */ async executeSubtask(subtask, plan, browsingConfig) {\n        try {\n            switch(subtask.type){\n                case 'search':\n                    return await this.executeSearchSubtask(subtask, plan);\n                case 'navigate':\n                    return await this.executeNavigateSubtask(subtask, plan);\n                case 'extract':\n                    return await this.executeExtractSubtask(subtask, plan);\n                case 'analyze':\n                    return await this.executeAnalyzeSubtask(subtask, plan, browsingConfig);\n                default:\n                    throw new Error(`Unknown subtask type: ${subtask.type}`);\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Execute a search subtask with advanced BrowserQL capabilities and smart query refinement\n   */ async executeSearchSubtask(subtask, plan) {\n        // Build context-aware query using results from previous subtasks\n        const contextualQuery = this.buildContextualQuery(subtask, plan);\n        const searchTerms = [\n            contextualQuery,\n            subtask.query,\n            ...subtask.searchTerms || []\n        ];\n        let lastError = '';\n        let bestResults = null;\n        let bestScore = 0;\n        console.log(`[Smart Browsing] 🔍 Advanced search execution for: \"${contextualQuery}\"`);\n        console.log(`[Smart Browsing] 🧠 Using context from ${plan.completedSubtasks.length} completed subtasks`);\n        // Check if this requires complex automation (flight booking, form filling, etc.)\n        const requiresComplexAutomation = this.detectComplexAutomationNeeds(subtask, contextualQuery);\n        if (requiresComplexAutomation) {\n            console.log(`[Smart Browsing] 🤖 Detected complex automation needs for: ${contextualQuery}`);\n            return await this.executeComplexAutomationSearch(subtask, plan, contextualQuery);\n        }\n        // Try different search terms with enhanced result evaluation\n        for (const searchTerm of searchTerms){\n            try {\n                console.log(`[Smart Browsing] 🔍 Searching with enhanced parsing: \"${searchTerm}\"`);\n                plan.searchQueries.push(searchTerm);\n                // Use the enhanced search with better snippet extraction\n                const result = await this.browserlessService.searchAndExtractUnblocked(searchTerm);\n                if (result.data && result.data.results && result.data.results.length > 0) {\n                    console.log(`[Smart Browsing] ✅ Found ${result.data.results.length} results with enhanced snippets for: \"${searchTerm}\"`);\n                    // Calculate result quality score based on snippet content and relevance\n                    const resultScore = this.calculateResultQuality(result.data.results, subtask.query);\n                    console.log(`[Smart Browsing] 📊 Result quality score: ${resultScore.toFixed(2)} for \"${searchTerm}\"`);\n                    // Store URLs we've found\n                    result.data.results.forEach((item)=>{\n                        if (item.link && !plan.visitedUrls.includes(item.link)) {\n                            plan.visitedUrls.push(item.link);\n                        }\n                    });\n                    // Keep track of best results\n                    if (resultScore > bestScore) {\n                        bestScore = resultScore;\n                        bestResults = result.data;\n                    }\n                    // If we found high-quality results, use them immediately\n                    if (resultScore > 0.8) {\n                        console.log(`[Smart Browsing] 🎯 High-quality results found (score: ${resultScore.toFixed(2)}), using immediately`);\n                        return {\n                            success: true,\n                            data: result.data\n                        };\n                    }\n                } else {\n                    lastError = `No results found for: \"${searchTerm}\"`;\n                    console.log(`[Smart Browsing] ⚠️ ${lastError}`);\n                }\n            } catch (error) {\n                lastError = error instanceof Error ? error.message : 'Search failed';\n                console.log(`[Smart Browsing] ❌ Search error for \"${searchTerm}\": ${lastError}`);\n            }\n        }\n        // Return best results if we found any\n        if (bestResults && bestScore > 0.3) {\n            console.log(`[Smart Browsing] ✅ Using best results with score: ${bestScore.toFixed(2)}`);\n            return {\n                success: true,\n                data: bestResults\n            };\n        }\n        return {\n            success: false,\n            error: `All search terms failed to find quality results. Last error: ${lastError}`\n        };\n    }\n    /**\n   * Calculate result quality score based on snippet content and relevance\n   */ calculateResultQuality(results, originalQuery) {\n        if (!results || results.length === 0) return 0;\n        let totalScore = 0;\n        const queryWords = originalQuery.toLowerCase().split(/\\s+/).filter((word)=>word.length > 2);\n        const currentYear = new Date().getFullYear().toString();\n        for (const result of results){\n            let score = 0;\n            const title = (result.title || '').toLowerCase();\n            const snippet = (result.snippet || '').toLowerCase();\n            const combinedText = `${title} ${snippet}`;\n            // Base score for having content\n            if (snippet && snippet !== 'no description available') {\n                score += 0.3;\n            }\n            // Relevance score based on query word matches\n            const matchedWords = queryWords.filter((word)=>combinedText.includes(word));\n            score += matchedWords.length / queryWords.length * 0.4;\n            // Bonus for current year (indicates recent/current information)\n            if (combinedText.includes(currentYear)) {\n                score += 0.2;\n            }\n            // Bonus for temporal keywords indicating current information\n            const temporalKeywords = [\n                'latest',\n                'recent',\n                'current',\n                'new',\n                '2025',\n                'updated'\n            ];\n            const temporalMatches = temporalKeywords.filter((keyword)=>combinedText.includes(keyword));\n            score += Math.min(temporalMatches.length * 0.1, 0.3);\n            // Penalty for very short snippets\n            if (snippet.length < 50) {\n                score *= 0.7;\n            }\n            // Bonus for longer, more informative snippets\n            if (snippet.length > 150) {\n                score += 0.1;\n            }\n            totalScore += score;\n        }\n        return Math.min(totalScore / results.length, 1.0);\n    }\n    /**\n   * Execute navigate subtask with advanced automation capabilities\n   */ async executeNavigateSubtask(subtask, plan) {\n        try {\n            const query = subtask.query;\n            console.log(`[Smart Browsing] 🌐 Processing advanced navigation task: ${query}`);\n            // Check if the query is a valid URL\n            const urlPattern = /^https?:\\/\\//i;\n            if (urlPattern.test(query)) {\n                // Direct URL navigation with automation detection\n                console.log(`[Smart Browsing] 🌐 Navigating to URL with automation support: ${query}`);\n                if (!plan.visitedUrls.includes(query)) {\n                    plan.visitedUrls.push(query);\n                }\n                // Check if this requires complex automation\n                const requiresComplexAutomation = this.detectComplexAutomationNeeds(subtask, query);\n                if (requiresComplexAutomation) {\n                    console.log(`[Smart Browsing] 🤖 Using complex automation workflow for: ${query}`);\n                    return await this.executeComplexAutomation(subtask, query, plan);\n                } else {\n                    // Standard navigation with enhanced error handling\n                    const result = await this.browserlessService.navigateAndExtract(query);\n                    if (result.data) {\n                        console.log(`[Smart Browsing] ✅ Navigation successful for: ${query}`);\n                        return {\n                            success: true,\n                            data: result.data\n                        };\n                    } else {\n                        return {\n                            success: false,\n                            error: 'No data extracted from navigation'\n                        };\n                    }\n                }\n            } else {\n                // Not a direct URL - convert to search task with enhanced query processing\n                console.log(`[Smart Browsing] 🔄 Converting navigation to enhanced search: ${query}`);\n                // Extract meaningful search terms from the navigation description\n                let searchQuery = query;\n                if (query.toLowerCase().includes('navigate to')) {\n                    searchQuery = query.replace(/navigate to\\s*/i, '').trim();\n                }\n                if (query.toLowerCase().includes('websites of')) {\n                    searchQuery = searchQuery.replace(/websites of\\s*/i, '').trim();\n                }\n                // Add current year for better results\n                const currentYear = new Date().getFullYear();\n                if (!searchQuery.includes(currentYear.toString())) {\n                    searchQuery = `${searchQuery} ${currentYear}`;\n                }\n                // Use the enhanced search functionality\n                return await this.executeSearchSubtask({\n                    ...subtask,\n                    type: 'search',\n                    query: searchQuery,\n                    searchTerms: [\n                        searchQuery,\n                        `${searchQuery} latest`,\n                        `${searchQuery} official website`,\n                        `${searchQuery} information`\n                    ]\n                }, plan);\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Navigation failed';\n            console.log(`[Smart Browsing] ❌ Navigation error: ${errorMessage}`);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Detect if a subtask requires complex automation (forms, CAPTCHAs, etc.)\n   */ detectComplexAutomationNeeds(subtask, url) {\n        const description = (subtask.description || '').toLowerCase();\n        const query = (subtask.query || '').toLowerCase();\n        // Keywords that indicate complex automation needs\n        const complexKeywords = [\n            'form',\n            'submit',\n            'login',\n            'register',\n            'book',\n            'reserve',\n            'purchase',\n            'checkout',\n            'payment',\n            'captcha',\n            'verify',\n            'authenticate',\n            'sign in',\n            'sign up',\n            'create account',\n            'fill out',\n            'application',\n            'survey',\n            'reservation',\n            'booking',\n            'order'\n        ];\n        // Flight booking specific keywords\n        const flightBookingKeywords = [\n            'earliest flight',\n            'flight from',\n            'flight to',\n            'book flight',\n            'search flight',\n            'flight booking',\n            'airline',\n            'departure',\n            'arrival',\n            'connecting flight',\n            'flight schedule',\n            'flight search',\n            'travel booking'\n        ];\n        // URL patterns that often require complex automation\n        const complexUrlPatterns = [\n            'login',\n            'register',\n            'checkout',\n            'booking',\n            'reservation',\n            'form',\n            'application',\n            'survey',\n            'account',\n            'dashboard',\n            'admin',\n            'portal'\n        ];\n        // Check for flight booking scenarios\n        const isFlightBooking = flightBookingKeywords.some((keyword)=>description.includes(keyword) || query.includes(keyword));\n        // Check for general complex automation needs\n        const isComplexAutomation = complexKeywords.some((keyword)=>description.includes(keyword) || query.includes(keyword)) || complexUrlPatterns.some((pattern)=>url.toLowerCase().includes(pattern));\n        // Flight booking always requires complex automation for form filling\n        return isFlightBooking || isComplexAutomation;\n    }\n    /**\n   * Execute complex automation workflow using advanced BrowserQL capabilities\n   */ async executeComplexAutomation(subtask, url, plan) {\n        try {\n            console.log(`[Smart Browsing] 🤖 Starting complex automation for: ${url}`);\n            // Create a workflow based on the subtask requirements\n            const workflow = this.buildAutomationWorkflow(subtask, url);\n            const result = await this.browserlessService.executeComplexWorkflow(url, workflow);\n            if (result && result.data) {\n                console.log(`[Smart Browsing] ✅ Complex automation successful for: ${url}`);\n                // Store session info for potential follow-up tasks\n                if (result.sessionId && result.reconnectUrl) {\n                    plan.sessionInfo = {\n                        sessionId: result.sessionId,\n                        reconnectUrl: result.reconnectUrl,\n                        lastUsed: new Date()\n                    };\n                }\n                return {\n                    success: true,\n                    data: result.data\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'Complex automation completed but no data extracted'\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Complex automation failed';\n            console.log(`[Smart Browsing] ❌ Complex automation error: ${errorMessage}`);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Build automation workflow steps based on subtask requirements\n   */ buildAutomationWorkflow(subtask, url) {\n        const workflow = [];\n        const description = (subtask.description || '').toLowerCase();\n        const query = (subtask.query || '').toLowerCase();\n        // Always start with navigation\n        workflow.push({\n            name: 'navigate',\n            type: 'navigate',\n            params: {\n                url,\n                waitUntil: 'networkIdle'\n            }\n        });\n        // Add wait for page to stabilize\n        workflow.push({\n            name: 'wait_for_page',\n            type: 'wait',\n            params: {\n                time: 2000\n            }\n        });\n        // Handle CAPTCHA if likely to be present\n        if (description.includes('captcha') || description.includes('verify') || description.includes('challenge') || description.includes('protection')) {\n            workflow.push({\n                name: 'solve_captcha',\n                type: 'captcha',\n                params: {\n                    type: 'hcaptcha'\n                } // Default to hCaptcha, can detect others\n            });\n        }\n        // Handle form filling\n        if (description.includes('form') || description.includes('fill') || description.includes('submit') || description.includes('input')) {\n            workflow.push({\n                name: 'wait_for_form',\n                type: 'wait',\n                params: {\n                    time: 1000\n                }\n            });\n            // Extract form structure for analysis\n            workflow.push({\n                name: 'analyze_form',\n                type: 'extract',\n                params: {\n                    selector: 'form, input, textarea, select',\n                    type: 'html'\n                }\n            });\n        }\n        // Handle login/authentication\n        if (description.includes('login') || description.includes('sign in') || description.includes('authenticate') || description.includes('credentials')) {\n            workflow.push({\n                name: 'wait_for_login_form',\n                type: 'wait',\n                params: {\n                    time: 1500\n                }\n            });\n            workflow.push({\n                name: 'extract_login_form',\n                type: 'extract',\n                params: {\n                    selector: 'form[action*=\"login\"], form[id*=\"login\"], input[type=\"password\"]',\n                    type: 'html'\n                }\n            });\n        }\n        // Handle infinite scroll if needed\n        if (description.includes('scroll') || description.includes('load more') || description.includes('pagination') || description.includes('infinite')) {\n            workflow.push({\n                name: 'scroll_content',\n                type: 'scroll',\n                params: {\n                    direction: 'down',\n                    distance: 1000\n                }\n            });\n            workflow.push({\n                name: 'wait_after_scroll',\n                type: 'wait',\n                params: {\n                    time: 2000\n                }\n            });\n            // Repeat scroll if needed\n            workflow.push({\n                name: 'scroll_more',\n                type: 'scroll',\n                params: {\n                    direction: 'down',\n                    distance: 1000\n                }\n            });\n        }\n        // Handle booking/reservation specific workflows\n        if (description.includes('book') || description.includes('reserve') || description.includes('appointment') || description.includes('schedule')) {\n            workflow.push({\n                name: 'wait_for_booking_interface',\n                type: 'wait',\n                params: {\n                    time: 2000\n                }\n            });\n            workflow.push({\n                name: 'extract_booking_options',\n                type: 'extract',\n                params: {\n                    selector: '.booking, .reservation, .calendar, .schedule, [class*=\"book\"], [class*=\"reserve\"]',\n                    type: 'html'\n                }\n            });\n        }\n        // Always end with comprehensive content extraction\n        workflow.push({\n            name: 'extract_main_content',\n            type: 'extract',\n            params: {\n                selector: 'main, .main, .content, .container, body',\n                type: 'text'\n            }\n        });\n        // Extract structured data if available\n        workflow.push({\n            name: 'extract_structured_data',\n            type: 'extract',\n            params: {\n                selector: '[itemscope], [data-*], .product, .article, .post',\n                type: 'html'\n            }\n        });\n        // Take screenshot for debugging/verification\n        workflow.push({\n            name: 'take_screenshot',\n            type: 'screenshot',\n            params: {\n                fullPage: false\n            }\n        });\n        console.log(`[Smart Browsing] 🔧 Built automation workflow with ${workflow.length} steps for: ${url}`);\n        return workflow;\n    }\n    /**\n   * Build specialized workflow for flight booking automation\n   */ buildFlightBookingWorkflow(subtask, plan, site) {\n        const workflow = [];\n        const description = subtask.description.toLowerCase();\n        // Get context from previous subtasks\n        const context = this.getFlightBookingContext(plan);\n        console.log(`[Smart Browsing] 🛫 Building flight booking workflow for: ${site}`);\n        console.log(`[Smart Browsing] 📋 Context:`, context);\n        // Navigate to the booking site\n        workflow.push({\n            name: 'navigate_to_booking_site',\n            type: 'navigate',\n            params: {\n                url: site,\n                waitUntil: 'networkIdle'\n            }\n        });\n        // Wait for page to load\n        workflow.push({\n            name: 'wait_for_page_load',\n            type: 'wait',\n            params: {\n                time: 3000\n            }\n        });\n        // Handle cookie banners and popups\n        workflow.push({\n            name: 'dismiss_popups',\n            type: 'click',\n            params: {\n                selector: '[data-testid=\"cookie-banner-close\"], .cookie-banner button, .modal-close, [aria-label=\"Close\"]',\n                optional: true\n            }\n        });\n        // Fill origin airport with site-specific selectors\n        if (context.origin) {\n            const originSelectors = this.getOriginSelectors(site);\n            workflow.push({\n                name: 'fill_origin',\n                type: 'type',\n                params: {\n                    selector: originSelectors.join(', '),\n                    text: context.origin,\n                    clear: true\n                }\n            });\n            // Wait for autocomplete dropdown\n            workflow.push({\n                name: 'wait_for_origin_autocomplete',\n                type: 'wait',\n                params: {\n                    time: 1500\n                }\n            });\n            // Select first autocomplete option\n            workflow.push({\n                name: 'select_origin',\n                type: 'click',\n                params: {\n                    selector: '.autocomplete-item:first-child, .suggestion:first-child, li:first-child, [role=\"option\"]:first-child',\n                    optional: true\n                }\n            });\n        }\n        // Fill destination airport with site-specific selectors\n        if (context.destination) {\n            const destinationSelectors = this.getDestinationSelectors(site);\n            workflow.push({\n                name: 'fill_destination',\n                type: 'type',\n                params: {\n                    selector: destinationSelectors.join(', '),\n                    text: context.destination,\n                    clear: true\n                }\n            });\n            // Wait for autocomplete dropdown\n            workflow.push({\n                name: 'wait_for_destination_autocomplete',\n                type: 'wait',\n                params: {\n                    time: 1500\n                }\n            });\n            // Select first autocomplete option\n            workflow.push({\n                name: 'select_destination',\n                type: 'click',\n                params: {\n                    selector: '.autocomplete-item:first-child, .suggestion:first-child, li:first-child, [role=\"option\"]:first-child',\n                    optional: true\n                }\n            });\n        }\n        // Set departure date (today) with site-specific selectors\n        const dateSelectors = this.getDateSelectors(site);\n        workflow.push({\n            name: 'click_departure_date',\n            type: 'click',\n            params: {\n                selector: dateSelectors.join(', ')\n            }\n        });\n        // Wait for date picker to open\n        workflow.push({\n            name: 'wait_for_date_picker',\n            type: 'wait',\n            params: {\n                time: 1000\n            }\n        });\n        // Select today's date with multiple selector strategies\n        const today = new Date();\n        const todayFormatted = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;\n        const todaySelectors = [\n            `[data-date=\"${todayFormatted}\"]`,\n            `[aria-label*=\"${today.getDate()}\"]`,\n            '.today',\n            '.current-day',\n            '.selected',\n            `button:contains(\"${today.getDate()}\")`,\n            `td:contains(\"${today.getDate()}\")`\n        ];\n        workflow.push({\n            name: 'select_today',\n            type: 'click',\n            params: {\n                selector: todaySelectors.join(', '),\n                optional: true\n            }\n        });\n        // Set one-way trip if this is for connecting flights\n        if (description.includes('connecting') || description.includes('after arrival')) {\n            workflow.push({\n                name: 'select_one_way',\n                type: 'click',\n                params: {\n                    selector: 'input[value=\"oneway\"], label[for*=\"oneway\"], .trip-type .one-way, [data-testid*=\"oneway\"]',\n                    optional: true\n                }\n            });\n        }\n        // Wait before submitting to ensure all fields are filled\n        workflow.push({\n            name: 'wait_before_submit',\n            type: 'wait',\n            params: {\n                time: 2000\n            }\n        });\n        // Submit search with site-specific selectors\n        const searchButtonSelectors = this.getSearchButtonSelectors(site);\n        workflow.push({\n            name: 'submit_search',\n            type: 'click',\n            params: {\n                selector: searchButtonSelectors.join(', ')\n            }\n        });\n        // Wait for results to load\n        workflow.push({\n            name: 'wait_for_results',\n            type: 'wait',\n            params: {\n                time: 5000\n            }\n        });\n        // Handle loading states\n        workflow.push({\n            name: 'wait_for_loading_complete',\n            type: 'waitForSelector',\n            params: {\n                selector: '.flight-results, .search-results, [data-testid=\"results\"]',\n                timeout: 15000\n            }\n        });\n        // Extract flight information\n        workflow.push({\n            name: 'extract_flight_results',\n            type: 'extract',\n            params: {\n                selector: '.flight-result, .flight-option, .search-result, [data-testid*=\"flight\"]',\n                type: 'html'\n            }\n        });\n        // Extract specific flight times and details\n        workflow.push({\n            name: 'extract_flight_details',\n            type: 'extract',\n            params: {\n                selector: '.departure-time, .arrival-time, .flight-duration, .airline-name, .price',\n                type: 'text'\n            }\n        });\n        // Take screenshot for verification\n        workflow.push({\n            name: 'take_results_screenshot',\n            type: 'screenshot',\n            params: {\n                fullPage: false\n            }\n        });\n        console.log(`[Smart Browsing] 🛫 Built flight booking workflow with ${workflow.length} steps`);\n        return workflow;\n    }\n    /**\n   * Get flight booking context from completed subtasks\n   */ getFlightBookingContext(plan) {\n        const context = {\n            origin: null,\n            destination: null,\n            departureTime: null,\n            arrivalTime: null,\n            duration: null\n        };\n        // Extract context from the original query and plan goal\n        const queryLower = plan.originalQuery.toLowerCase();\n        const goalLower = plan.goal.toLowerCase();\n        // Extract origin and destination from query\n        if (queryLower.includes('owerri') && queryLower.includes('abuja')) {\n            context.origin = 'Owerri';\n            context.destination = 'Abuja';\n        } else if (queryLower.includes('abuja') && queryLower.includes('dubai')) {\n            context.origin = 'Abuja';\n            context.destination = 'Dubai';\n        }\n        // Extract any timing information from completed subtasks\n        const completedSubtasks = plan.subtasks.filter((task)=>task.status === 'completed' && task.result);\n        for (const task of completedSubtasks){\n            const result = JSON.stringify(task.result).toLowerCase();\n            // Look for departure times\n            const timeMatch = result.match(/(\\d{1,2}:\\d{2}(?:\\s*(?:am|pm))?)/);\n            if (timeMatch && !context.departureTime) {\n                context.departureTime = timeMatch[1];\n            }\n            // Look for duration\n            const durationMatch = result.match(/(\\d+(?:\\s*(?:hours?|hrs?|h))?(?:\\s*(?:and|\\&)?\\s*\\d+(?:\\s*(?:minutes?|mins?|m))?)?)/);\n            if (durationMatch && !context.duration) {\n                context.duration = durationMatch[1];\n            }\n        }\n        return context;\n    }\n    /**\n   * Get site-specific origin selectors\n   */ getOriginSelectors(site) {\n        const siteHost = new URL(site).hostname.toLowerCase();\n        const commonSelectors = [\n            'input[placeholder*=\"From\"]',\n            'input[name*=\"origin\"]',\n            'input[id*=\"from\"]',\n            'input[aria-label*=\"From\"]',\n            'input[data-testid*=\"origin\"]',\n            'input[data-testid*=\"from\"]'\n        ];\n        if (siteHost.includes('kayak')) {\n            return [\n                'input[placeholder*=\"From\"]',\n                'input[aria-label*=\"Flight origin\"]',\n                'input[data-testid=\"origin\"]',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('expedia')) {\n            return [\n                'input[id*=\"flight-origin\"]',\n                'input[aria-label*=\"Leaving from\"]',\n                'input[data-testid*=\"origin\"]',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('skyscanner')) {\n            return [\n                'input[placeholder*=\"From\"]',\n                'input[data-testid*=\"origin\"]',\n                'input[name*=\"OriginPlace\"]',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('google')) {\n            return [\n                'input[placeholder*=\"Where from\"]',\n                'input[aria-label*=\"Where from\"]',\n                'input[jsname*=\"origin\"]',\n                ...commonSelectors\n            ];\n        }\n        return commonSelectors;\n    }\n    /**\n   * Get site-specific destination selectors\n   */ getDestinationSelectors(site) {\n        const siteHost = new URL(site).hostname.toLowerCase();\n        const commonSelectors = [\n            'input[placeholder*=\"To\"]',\n            'input[name*=\"destination\"]',\n            'input[id*=\"to\"]',\n            'input[aria-label*=\"To\"]',\n            'input[data-testid*=\"destination\"]',\n            'input[data-testid*=\"to\"]'\n        ];\n        if (siteHost.includes('kayak')) {\n            return [\n                'input[placeholder*=\"To\"]',\n                'input[aria-label*=\"Flight destination\"]',\n                'input[data-testid=\"destination\"]',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('expedia')) {\n            return [\n                'input[id*=\"flight-destination\"]',\n                'input[aria-label*=\"Going to\"]',\n                'input[data-testid*=\"destination\"]',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('skyscanner')) {\n            return [\n                'input[placeholder*=\"To\"]',\n                'input[data-testid*=\"destination\"]',\n                'input[name*=\"DestinationPlace\"]',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('google')) {\n            return [\n                'input[placeholder*=\"Where to\"]',\n                'input[aria-label*=\"Where to\"]',\n                'input[jsname*=\"destination\"]',\n                ...commonSelectors\n            ];\n        }\n        return commonSelectors;\n    }\n    /**\n   * Get site-specific date selectors\n   */ getDateSelectors(site) {\n        const siteHost = new URL(site).hostname.toLowerCase();\n        const commonSelectors = [\n            'input[placeholder*=\"Departure\"]',\n            'input[name*=\"departure\"]',\n            'input[id*=\"depart\"]',\n            'input[data-testid*=\"departure\"]',\n            'button[data-testid*=\"departure\"]'\n        ];\n        if (siteHost.includes('kayak')) {\n            return [\n                'input[aria-label*=\"Start date\"]',\n                'input[data-testid=\"departure-date\"]',\n                'button[data-testid=\"departure-date\"]',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('expedia')) {\n            return [\n                'input[id*=\"flight-departing\"]',\n                'button[data-testid*=\"departure\"]',\n                'input[aria-label*=\"Departing\"]',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('skyscanner')) {\n            return [\n                'input[placeholder*=\"Depart\"]',\n                'button[data-testid*=\"depart\"]',\n                'input[name*=\"OutboundDate\"]',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('google')) {\n            return [\n                'input[placeholder*=\"Departure\"]',\n                'input[aria-label*=\"Departure\"]',\n                'div[data-value*=\"departure\"]',\n                ...commonSelectors\n            ];\n        }\n        return commonSelectors;\n    }\n    /**\n   * Get site-specific search button selectors\n   */ getSearchButtonSelectors(site) {\n        const siteHost = new URL(site).hostname.toLowerCase();\n        const commonSelectors = [\n            'button[type=\"submit\"]',\n            '.search-button',\n            'button:contains(\"Search\")',\n            '[data-testid=\"submit\"]',\n            'button[aria-label*=\"Search\"]'\n        ];\n        if (siteHost.includes('kayak')) {\n            return [\n                'button[aria-label*=\"Search\"]',\n                'button[data-testid=\"submit-button\"]',\n                '.Common-Widgets-Button-ButtonPrimary',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('expedia')) {\n            return [\n                'button[data-testid*=\"search\"]',\n                'button[aria-label*=\"Search\"]',\n                '.btn-primary',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('skyscanner')) {\n            return [\n                'button[data-testid*=\"search\"]',\n                'button:contains(\"Search flights\")',\n                '.BpkButton--primary',\n                ...commonSelectors\n            ];\n        } else if (siteHost.includes('google')) {\n            return [\n                'button[aria-label*=\"Search\"]',\n                'button[jsname*=\"search\"]',\n                '.VfPpkd-LgbsSe',\n                ...commonSelectors\n            ];\n        }\n        return commonSelectors;\n    }\n    /**\n   * Execute extract subtask\n   */ async executeExtractSubtask(subtask, plan) {\n        // Similar to navigate but with specific extraction focus\n        return this.executeNavigateSubtask(subtask, plan);\n    }\n    /**\n   * Execute analyze subtask - synthesize gathered information\n   */ async executeAnalyzeSubtask(subtask, plan, browsingConfig) {\n        try {\n            console.log(`[Smart Browsing] 🧠 Analyzing gathered data...`);\n            // Collect all data from completed subtasks\n            const gatheredData = plan.subtasks.filter((task)=>task.status === 'completed' && task.result).map((task)=>({\n                    type: task.type,\n                    description: task.description,\n                    query: task.query,\n                    data: task.result\n                }));\n            if (gatheredData.length === 0) {\n                return {\n                    success: false,\n                    error: 'No data available for analysis'\n                };\n            }\n            // Use AI to analyze and synthesize the data\n            const model = browsingConfig.browsing_models[0];\n            if (!model) {\n                return {\n                    success: false,\n                    error: 'No AI model available for analysis'\n                };\n            }\n            const analysisPrompt = this.buildAnalysisPrompt(plan.originalQuery, gatheredData);\n            const aiResult = await this.callAIForAnalysis(analysisPrompt, model);\n            if (aiResult.success && aiResult.content) {\n                return {\n                    success: true,\n                    data: {\n                        analysis: aiResult.content,\n                        sourceData: gatheredData\n                    }\n                };\n            } else {\n                return {\n                    success: false,\n                    error: aiResult.error || 'Analysis failed'\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Analysis failed'\n            };\n        }\n    }\n    /**\n   * Calculate progress percentage\n   */ calculateProgress(plan) {\n        const totalTasks = plan.subtasks.length;\n        const completedTasks = plan.completedSubtasks.length;\n        return Math.round(completedTasks / totalTasks * 100);\n    }\n    /**\n   * Log progress update\n   */ logProgress(plan) {\n        const completed = plan.completedSubtasks.length;\n        const failed = plan.failedSubtasks.length;\n        const total = plan.subtasks.length;\n        const remaining = total - completed - failed;\n        console.log(`[Smart Browsing] 📊 Progress: ${plan.progress}% (${completed}/${total} completed, ${failed} failed, ${remaining} remaining)`);\n    }\n    /**\n   * Build context-aware query using results from previous subtasks\n   */ buildContextualQuery(subtask, plan) {\n        let contextualQuery = subtask.query;\n        // Get context from completed subtasks\n        const completedSubtasks = plan.subtasks.filter((task)=>task.status === 'completed' && task.result && task.id !== subtask.id);\n        if (completedSubtasks.length === 0) {\n            return contextualQuery;\n        }\n        console.log(`[Smart Browsing] 🧠 Building contextual query from ${completedSubtasks.length} completed subtasks`);\n        // Extract key information from previous results\n        const contextInfo = [];\n        for (const completedTask of completedSubtasks){\n            const result = completedTask.result;\n            // Extract specific information based on task type and content\n            if (completedTask.description.toLowerCase().includes('flight') && completedTask.description.toLowerCase().includes('earliest')) {\n                // Extract flight times, airlines, etc.\n                const flightInfo = this.extractFlightInfo(result);\n                if (flightInfo) {\n                    contextInfo.push(flightInfo);\n                }\n            }\n            if (completedTask.description.toLowerCase().includes('duration')) {\n                // Extract duration information\n                const durationInfo = this.extractDurationInfo(result);\n                if (durationInfo) {\n                    contextInfo.push(durationInfo);\n                }\n            }\n            // Extract arrival times for connecting flights\n            if (completedTask.description.toLowerCase().includes('arrive')) {\n                const arrivalInfo = this.extractArrivalInfo(result);\n                if (arrivalInfo) {\n                    contextInfo.push(arrivalInfo);\n                }\n            }\n        }\n        // Enhance the query with context\n        if (contextInfo.length > 0) {\n            const context = contextInfo.join(', ');\n            // Build more specific contextual queries based on subtask type\n            if (subtask.description.toLowerCase().includes('connecting') || subtask.description.toLowerCase().includes('after arrival')) {\n                // For connecting flights, calculate departure time based on arrival + buffer\n                const arrivalTime = this.extractTimeFromContext(contextInfo);\n                if (arrivalTime) {\n                    const departureTime = this.calculateConnectingFlightTime(arrivalTime);\n                    contextualQuery = `${subtask.query} departing after ${departureTime}`;\n                } else {\n                    contextualQuery = `${subtask.query} departing after ${context}`;\n                }\n            } else if (subtask.description.toLowerCase().includes('duration') && contextInfo.some((info)=>info.includes('flight'))) {\n                // For duration queries, be more specific about which flight\n                const flightInfo = contextInfo.find((info)=>info.includes('flight'));\n                contextualQuery = `${subtask.query} for ${flightInfo}`;\n            } else if (subtask.description.toLowerCase().includes('earliest') && contextInfo.some((info)=>info.includes('arrives'))) {\n                // For earliest flight queries after knowing arrival time\n                const arrivalInfo = contextInfo.find((info)=>info.includes('arrives'));\n                contextualQuery = `${subtask.query} after ${arrivalInfo}`;\n            } else {\n                contextualQuery = `${subtask.query} (context: ${context})`;\n            }\n            console.log(`[Smart Browsing] 🎯 Enhanced query with context: \"${contextualQuery}\"`);\n        }\n        return contextualQuery;\n    }\n    /**\n   * Extract flight information from search results\n   */ extractFlightInfo(result) {\n        if (!result) return null;\n        try {\n            const resultStr = JSON.stringify(result).toLowerCase();\n            // Look for flight times (e.g., \"06:30\", \"6:30 am\", etc.)\n            const timePattern = /(\\d{1,2}:\\d{2}(?:\\s*(?:am|pm))?)/gi;\n            const times = resultStr.match(timePattern);\n            // Look for airlines\n            const airlinePattern = /(arik air|air peace|dana air|azman air|emirates|qatar|turkish)/gi;\n            const airlines = resultStr.match(airlinePattern);\n            if (times && times.length > 0) {\n                const earliestTime = times[0];\n                const airline = airlines ? airlines[0] : '';\n                return `earliest flight ${earliestTime}${airline ? ` on ${airline}` : ''}`;\n            }\n        } catch (error) {\n            console.warn('[Smart Browsing] Error extracting flight info:', error);\n        }\n        return null;\n    }\n    /**\n   * Extract duration information from search results\n   */ extractDurationInfo(result) {\n        if (!result) return null;\n        try {\n            const resultStr = JSON.stringify(result).toLowerCase();\n            // Look for duration patterns (e.g., \"2 hours 30 minutes\", \"2h 30m\", etc.)\n            const durationPattern = /(\\d+(?:\\s*(?:hours?|hrs?|h))?(?:\\s*(?:and|\\&)?\\s*\\d+(?:\\s*(?:minutes?|mins?|m))?)?)/gi;\n            const durations = resultStr.match(durationPattern);\n            if (durations && durations.length > 0) {\n                return `duration ${durations[0]}`;\n            }\n        } catch (error) {\n            console.warn('[Smart Browsing] Error extracting duration info:', error);\n        }\n        return null;\n    }\n    /**\n   * Extract arrival time information from search results\n   */ extractArrivalInfo(result) {\n        if (!result) return null;\n        try {\n            const resultStr = JSON.stringify(result).toLowerCase();\n            // Look for arrival times\n            const arrivalPattern = /(?:arrives?|arrival|landing)(?:\\s*(?:at|in|by))?\\s*(\\d{1,2}:\\d{2}(?:\\s*(?:am|pm))?)/gi;\n            const arrivals = resultStr.match(arrivalPattern);\n            if (arrivals && arrivals.length > 0) {\n                return `arrives ${arrivals[0]}`;\n            }\n        } catch (error) {\n            console.warn('[Smart Browsing] Error extracting arrival info:', error);\n        }\n        return null;\n    }\n    /**\n   * Extract time information from context\n   */ extractTimeFromContext(contextInfo) {\n        for (const info of contextInfo){\n            const timeMatch = info.match(/(\\d{1,2}:\\d{2}(?:\\s*(?:am|pm))?)/i);\n            if (timeMatch) {\n                return timeMatch[1];\n            }\n        }\n        return null;\n    }\n    /**\n   * Calculate connecting flight departure time with buffer\n   */ calculateConnectingFlightTime(arrivalTime) {\n        try {\n            // Parse the arrival time\n            const timeMatch = arrivalTime.match(/(\\d{1,2}):(\\d{2})(?:\\s*(am|pm))?/i);\n            if (!timeMatch) return arrivalTime;\n            let hours = parseInt(timeMatch[1]);\n            const minutes = parseInt(timeMatch[2]);\n            const ampm = timeMatch[3]?.toLowerCase();\n            // Convert to 24-hour format\n            if (ampm === 'pm' && hours !== 12) {\n                hours += 12;\n            } else if (ampm === 'am' && hours === 12) {\n                hours = 0;\n            }\n            // Add 2-hour buffer for international connections, 1 hour for domestic\n            const bufferHours = 2; // Assume international for safety\n            hours += bufferHours;\n            // Handle day overflow\n            if (hours >= 24) {\n                hours -= 24;\n            }\n            // Format back to readable time\n            const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;\n            const displayAmPm = hours >= 12 ? 'PM' : 'AM';\n            return `${displayHours}:${String(minutes).padStart(2, '0')} ${displayAmPm}`;\n        } catch (error) {\n            console.warn('[Smart Browsing] Error calculating connecting flight time:', error);\n            return arrivalTime;\n        }\n    }\n    /**\n   * Enhance subtask query with dynamic context-aware modifications\n   */ enhanceSubtaskQuery(subtask, plan) {\n        const enhancedSubtask = {\n            ...subtask\n        };\n        // Get context from completed subtasks\n        const completedSubtasks = plan.subtasks.filter((task)=>task.status === 'completed' && task.result && task.id !== subtask.id);\n        if (completedSubtasks.length === 0) {\n            return enhancedSubtask;\n        }\n        // Enhance search terms based on context\n        const contextualTerms = [];\n        for (const completedTask of completedSubtasks){\n            const result = completedTask.result;\n            // Extract specific information for search term enhancement\n            if (completedTask.description.toLowerCase().includes('earliest flight')) {\n                const flightInfo = this.extractFlightInfo(result);\n                if (flightInfo) {\n                    // Add specific flight details to search terms\n                    contextualTerms.push(`${subtask.query} ${flightInfo}`);\n                    contextualTerms.push(`${subtask.query} after ${flightInfo}`);\n                }\n            }\n            if (completedTask.description.toLowerCase().includes('duration')) {\n                const durationInfo = this.extractDurationInfo(result);\n                if (durationInfo) {\n                    // Use duration info for more specific searches\n                    contextualTerms.push(`${subtask.query} ${durationInfo}`);\n                }\n            }\n        }\n        // Add contextual terms to search terms\n        if (contextualTerms.length > 0) {\n            enhancedSubtask.searchTerms = [\n                ...enhancedSubtask.searchTerms || [],\n                ...contextualTerms\n            ];\n            console.log(`[Smart Browsing] 🎯 Enhanced subtask with ${contextualTerms.length} contextual search terms`);\n        }\n        return enhancedSubtask;\n    }\n    /**\n   * Execute complex automation search for flight booking and form filling\n   */ async executeComplexAutomationSearch(subtask, plan, query) {\n        console.log(`[Smart Browsing] 🤖 Starting complex automation search for: ${query}`);\n        // Determine the best booking site to use\n        const bookingSites = [\n            'https://www.kayak.com',\n            'https://www.expedia.com',\n            'https://www.skyscanner.com',\n            'https://www.google.com/flights'\n        ];\n        for (const site of bookingSites){\n            try {\n                console.log(`[Smart Browsing] 🌐 Attempting automation on: ${site}`);\n                // Build automation workflow for flight booking\n                const workflow = this.buildFlightBookingWorkflow(subtask, plan, site);\n                const result = await this.browserlessService.executeComplexWorkflow(site, workflow);\n                if (result && result.data) {\n                    console.log(`[Smart Browsing] ✅ Complex automation successful on: ${site}`);\n                    return {\n                        success: true,\n                        data: result.data\n                    };\n                }\n            } catch (error) {\n                console.log(`[Smart Browsing] ❌ Automation failed on ${site}:`, error);\n                continue;\n            }\n        }\n        return {\n            success: false,\n            error: 'All booking sites failed automation'\n        };\n    }\n    /**\n   * Build analysis prompt for AI\n   */ buildAnalysisPrompt(originalQuery, gatheredData) {\n        // Get current date and time for context\n        const now = new Date();\n        const currentDateTime = now.toLocaleString('en-US', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit',\n            timeZoneName: 'short'\n        });\n        const dataContext = gatheredData.map((item, index)=>`Source ${index + 1} (${item.type}): ${item.description}\\nQuery: ${item.query}\\nData: ${JSON.stringify(item.data, null, 2)}`).join('\\n\\n---\\n\\n');\n        return `You are an expert information analyst. Analyze the following browsing data and provide a comprehensive answer to the original query.\n\nCURRENT DATE & TIME: ${currentDateTime}\nORIGINAL QUERY: \"${originalQuery}\"\n\nGATHERED DATA:\n${dataContext}\n\nPlease provide:\n1. A comprehensive answer to the original query\n2. Key findings and insights with temporal relevance\n3. Any conflicting information found\n4. Confidence level in the findings\n5. Recommendations for further research if needed\n6. Note the recency and relevance of the information found\n\nIMPORTANT: Consider the current date when analyzing the data. Prioritize recent information and note if any data appears outdated. For time-sensitive queries, emphasize the most current findings.\n\nFormat your response as a clear, well-structured analysis that directly addresses the user's query with temporal awareness.`;\n    }\n    /**\n   * Call AI for planning\n   */ async callAIForPlanning(prompt, model) {\n        try {\n            const effectiveModelId = this.getEffectiveModelId(model);\n            const messages = [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ];\n            let apiUrl;\n            let headers;\n            let body;\n            // Configure API call based on provider (same as BrowsingExecutionService)\n            switch(model.provider){\n                case 'openai':\n                    apiUrl = 'https://api.openai.com/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                case 'google':\n                    apiUrl = 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                case 'anthropic':\n                    apiUrl = 'https://api.anthropic.com/v1/messages';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'x-api-key': model.api_key,\n                        'anthropic-version': '2023-06-01'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                case 'openrouter':\n                    apiUrl = 'https://openrouter.ai/api/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`,\n                        'HTTP-Referer': 'https://roukey.online',\n                        'X-Title': 'RouKey'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                default:\n                    throw new Error(`Unsupported provider for planning: ${model.provider}`);\n            }\n            const response = await fetch(apiUrl, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify(body),\n                signal: AbortSignal.timeout(30000)\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(`API error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            let content;\n            if (model.provider === 'anthropic') {\n                content = result.content?.[0]?.text;\n            } else {\n                content = result.choices?.[0]?.message?.content;\n            }\n            if (!content) {\n                throw new Error('No content returned from AI model');\n            }\n            return {\n                success: true,\n                content\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Call AI for analysis (same as planning but different purpose)\n   */ async callAIForAnalysis(prompt, model) {\n        return this.callAIForPlanning(prompt, model); // Same implementation\n    }\n    /**\n   * Get effective model ID (same logic as BrowsingExecutionService)\n   */ getEffectiveModelId(model) {\n        if (model.provider.toLowerCase() === 'openrouter') {\n            return model.model;\n        }\n        const parts = model.model.split('/');\n        return parts.length > 1 ? parts[parts.length - 1] : model.model;\n    }\n    /**\n   * Parse plan from AI response\n   */ parsePlanFromAI(aiResponse, originalQuery) {\n        try {\n            // Try to extract JSON from the response\n            const jsonMatch = aiResponse.match(/\\{[\\s\\S]*\\}/);\n            if (!jsonMatch) {\n                throw new Error('No JSON found in AI response');\n            }\n            const parsed = JSON.parse(jsonMatch[0]);\n            if (!parsed.goal || !parsed.subtasks || !Array.isArray(parsed.subtasks)) {\n                throw new Error('Invalid plan structure from AI');\n            }\n            // Convert AI subtasks to our format\n            const subtasks = parsed.subtasks.map((task, index)=>({\n                    id: task.id || `ai_task_${Date.now()}_${index}`,\n                    type: task.type || 'search',\n                    description: task.description || `Task ${index + 1}`,\n                    query: task.query || originalQuery,\n                    status: 'pending',\n                    priority: task.priority || 5,\n                    attempts: 0,\n                    maxAttempts: 3,\n                    searchTerms: task.searchTerms || [],\n                    expectedInfo: task.expectedInfo || ''\n                }));\n            return {\n                goal: parsed.goal,\n                subtasks\n            };\n        } catch (error) {\n            console.warn('[Smart Browsing] Failed to parse AI plan, using fallback:', error);\n            return {\n                goal: `Find information about: ${originalQuery}`,\n                subtasks: this.createFallbackPlan(originalQuery, 'search')\n            };\n        }\n    }\n    /**\n   * Synthesize final result from all gathered data\n   */ async synthesizeFinalResult(plan, browsingConfig) {\n        try {\n            // Find the analysis result if available\n            const analysisTask = plan.subtasks.find((task)=>task.type === 'analyze' && task.status === 'completed' && task.result);\n            if (analysisTask && analysisTask.result?.analysis) {\n                return analysisTask.result.analysis;\n            }\n            // If no analysis task, create a summary from all completed tasks\n            const completedTasks = plan.subtasks.filter((task)=>task.status === 'completed' && task.result);\n            if (completedTasks.length === 0) {\n                return `No information was successfully gathered for the query: \"${plan.originalQuery}\"`;\n            }\n            // Create a basic summary\n            let summary = `Based on browsing research for \"${plan.originalQuery}\":\\n\\n`;\n            completedTasks.forEach((task, index)=>{\n                summary += `${index + 1}. ${task.description}:\\n`;\n                if (task.result?.results && Array.isArray(task.result.results)) {\n                    task.result.results.slice(0, 3).forEach((result)=>{\n                        summary += `   • ${result.title || 'Result'}\\n`;\n                    });\n                } else if (typeof task.result === 'string') {\n                    summary += `   ${task.result.substring(0, 200)}...\\n`;\n                }\n                summary += '\\n';\n            });\n            return summary;\n        } catch (error) {\n            console.error('[Smart Browsing] Error synthesizing final result:', error);\n            return `Research completed for \"${plan.originalQuery}\" but encountered errors in synthesis. Please check the individual results.`;\n        }\n    }\n    /**\n   * Log the browsing plan for debugging\n   */ logPlan(plan) {\n        console.log(`[Smart Browsing] 📋 BROWSING PLAN:`);\n        console.log(`[Smart Browsing] Goal: ${plan.goal}`);\n        console.log(`[Smart Browsing] Subtasks:`);\n        plan.subtasks.forEach((subtask, index)=>{\n            console.log(`[Smart Browsing]   ${index + 1}. [${subtask.type.toUpperCase()}] ${subtask.description}`);\n            console.log(`[Smart Browsing]      Query: \"${subtask.query}\"`);\n            console.log(`[Smart Browsing]      Priority: ${subtask.priority}, Status: ${subtask.status}`);\n            if (subtask.searchTerms && subtask.searchTerms.length > 0) {\n                console.log(`[Smart Browsing]      Alt terms: ${subtask.searchTerms.join(', ')}`);\n            }\n        });\n    }\n    /**\n   * Create or reuse browser session for complex workflows\n   */ async ensureBrowserSession(plan, initialUrl) {\n        // Check if we have an existing session that's still valid\n        if (plan.sessionInfo && plan.sessionInfo.reconnectUrl) {\n            const timeSinceLastUse = Date.now() - new Date(plan.sessionInfo.lastUsed).getTime();\n            const sessionTimeout = 10 * 60 * 1000; // 10 minutes\n            if (timeSinceLastUse < sessionTimeout) {\n                console.log(`[Smart Browsing] 🔄 Reusing existing browser session: ${plan.sessionInfo.sessionId}`);\n                // Update last used time\n                plan.sessionInfo.lastUsed = new Date();\n                return {\n                    sessionId: plan.sessionInfo.sessionId,\n                    reconnectUrl: plan.sessionInfo.reconnectUrl\n                };\n            } else {\n                console.log(`[Smart Browsing] ⏰ Session expired, creating new session`);\n            }\n        }\n        // Create new session\n        console.log(`[Smart Browsing] 🆕 Creating new browser session`);\n        const session = await this.browserlessService.createBrowsingSession(initialUrl || 'https://www.google.com', {\n            timeout: 600000,\n            humanLike: true\n        });\n        // Store session info in plan\n        plan.sessionInfo = {\n            sessionId: session.sessionId,\n            reconnectUrl: session.reconnectUrl,\n            lastUsed: new Date(),\n            activeWorkflow: true\n        };\n        console.log(`[Smart Browsing] ✅ Created browser session: ${session.sessionId}`);\n        return session;\n    }\n    /**\n   * Check if subtask requires persistent session\n   */ requiresPersistentSession(subtask) {\n        const description = subtask.description.toLowerCase();\n        const query = subtask.query.toLowerCase();\n        // Tasks that benefit from session persistence\n        const sessionKeywords = [\n            'form',\n            'booking',\n            'reservation',\n            'login',\n            'multi-step',\n            'workflow',\n            'navigation',\n            'complex',\n            'automation'\n        ];\n        // Flight booking always benefits from session persistence\n        const flightBookingKeywords = [\n            'flight',\n            'airline',\n            'booking',\n            'search flight',\n            'travel'\n        ];\n        return sessionKeywords.some((keyword)=>description.includes(keyword) || query.includes(keyword)) || flightBookingKeywords.some((keyword)=>description.includes(keyword) || query.includes(keyword));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browsing/SmartBrowsingExecutor.ts\n");

/***/ })

};
;